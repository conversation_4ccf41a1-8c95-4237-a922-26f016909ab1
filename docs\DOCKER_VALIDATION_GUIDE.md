# Docker-based HuggingFace Token Validation Guide

## 🎯 Overview

This guide shows you how to validate your HuggingFace token inside the exact same Docker environment that will be used for MedGemma deployment. This ensures that validation results match the actual runtime environment.

## 🚀 Quick Start

### Method 1: Simple Docker Validation (Recommended)
```bash
# Build and run validation in one command
./scripts/validate-hf-token-docker.sh

# Or with a specific token
./scripts/validate-hf-token-docker.sh hf_your_token_here
```

### Method 2: Docker Compose Validation
```bash
# Use Docker Compose for validation
./scripts/validate-hf-token-docker.sh --compose
```

### Method 3: Manual Docker Commands
```bash
# Build validation image
docker build -f docker/Dockerfile.validation -t medgemma-validation .

# Run validation
docker run --rm -e HUGGINGFACE_TOKEN=hf_your_token_here medgemma-validation
```

## 🔧 Why Docker-based Validation?

### **Exact Environment Match**
- ✅ Same Python version as deployment
- ✅ Same system dependencies
- ✅ Same HuggingFace Hub version
- ✅ Same network and security settings
- ✅ Same file permissions and user context

### **Fixes Permission Issues**
- ✅ Resolves `/app/model_cache/stored_tokens` permission errors
- ✅ Uses temporary cache directories
- ✅ Avoids writing to persistent storage during validation
- ✅ Matches the exact runtime environment

### **Comprehensive Testing**
- ✅ Token format validation
- ✅ HuggingFace API authentication
- ✅ Model access permissions
- ✅ File listing capabilities
- ✅ Download functionality

## 📋 What Gets Validated

### **Step 1: Environment Check**
- Python and HuggingFace Hub availability
- Network connectivity to HuggingFace
- Cache directory permissions

### **Step 2: Token Format**
- Validates `hf_` prefix
- Checks 37-character length
- Ensures alphanumeric format

### **Step 3: Authentication**
- Uses `HfApi(token=token).whoami()`
- Tests without writing to persistent storage
- Returns authenticated user information

### **Step 4: Model Access**
- Tests access to `google/medgemma-4b-it`
- Checks gated model permissions
- Validates license acceptance

### **Step 5: File Access**
- Lists model repository files
- Confirms download permissions
- Tests actual file access

### **Step 6: Download Test**
- Downloads `config.json` to temporary directory
- Validates file content
- Confirms end-to-end access

## 🛠️ Installation and Setup

### Prerequisites
```bash
# Ensure Docker is installed and running
docker --version
docker-compose --version

# Ensure Docker daemon is running
sudo systemctl start docker  # Linux
# or start Docker Desktop on macOS/Windows
```

### Build Validation Image
```bash
# The validation script will build automatically, or build manually:
docker build -f docker/Dockerfile.validation -t medgemma-validation .
```

## 🎯 Usage Examples

### Basic Validation
```bash
# Auto-detect token from environment
export HUGGINGFACE_TOKEN=hf_your_token_here
./scripts/validate-hf-token-docker.sh

# Use token from .env.phase1 file
echo 'HUGGINGFACE_TOKEN=hf_your_token_here' > .env.phase1
./scripts/validate-hf-token-docker.sh
```

### Advanced Validation
```bash
# Use Docker Compose method
./scripts/validate-hf-token-docker.sh --compose

# Validate specific token
./scripts/validate-hf-token-docker.sh hf_abc123def456...

# Get help
./scripts/validate-hf-token-docker.sh --help
```

### Manual Docker Commands
```bash
# Build validation image
docker build -f docker/Dockerfile.validation -t medgemma-validation .

# Run with environment variable
docker run --rm \
  -e HUGGINGFACE_TOKEN=hf_your_token_here \
  medgemma-validation

# Run with volume mount for logs
docker run --rm \
  -e HUGGINGFACE_TOKEN=hf_your_token_here \
  -v $(pwd)/data/validation:/app/validation/logs \
  medgemma-validation
```

## 📊 Expected Output

### Successful Validation
```
🧪 Running Docker-based HuggingFace Token Validation

[STEP 1] Validating token format...
[SUCCESS] Token format is valid

[STEP 2] Testing HuggingFace authentication...
[SUCCESS] Authentication successful
[INFO] Token validated for user: your-username

[STEP 3] Testing access to google/medgemma-4b-it...
[SUCCESS] Model access successful
[INFO] Model access confirmed: google/medgemma-4b-it
[WARNING] This is a gated model - license acceptance required

[STEP 4] Testing model file access...
[SUCCESS] Model files accessible
[INFO] Model files accessible: 15 files found

[STEP 5] Testing download capability...
[INFO] Downloading config.json for validation...
[SUCCESS] Download test successful
[INFO] Successfully parsed config.json
[INFO]   Model type: gemma
[INFO]   Vocabulary size: 256,000

📊 Docker Validation Summary
[SUCCESS] Passed: 5/5 tests
✅ All tests passed! Your token is ready for deployment.

🚀 The token works in the Docker environment!
```

## 🚨 Troubleshooting

### Docker Issues
```bash
# Check Docker is running
docker info

# Check if image builds successfully
docker build -f docker/Dockerfile.validation -t medgemma-validation .

# Check container logs
docker logs medgemma-token-validation
```

### Permission Issues
The Docker validation automatically handles permission issues by:
- Using temporary cache directories
- Running as non-root user
- Avoiding persistent storage writes during validation

### Token Issues
```bash
# Check token format
echo "Token length: ${#HUGGINGFACE_TOKEN}"
echo "Token prefix: ${HUGGINGFACE_TOKEN:0:3}"

# Test token manually
docker run --rm -e HUGGINGFACE_TOKEN=hf_your_token_here medgemma-validation
```

### Network Issues
```bash
# Test connectivity from container
docker run --rm alpine ping -c 3 huggingface.co

# Test with proxy if needed
docker run --rm -e HTTP_PROXY=your-proxy medgemma-validation
```

## 🔄 Integration with Deployment

### Pre-Deployment Validation
```bash
# Add to your deployment script
if ./scripts/validate-hf-token-docker.sh; then
    echo "✅ Token validated in Docker environment"
    docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d
else
    echo "❌ Token validation failed in Docker environment"
    exit 1
fi
```

### Automated CI/CD
```yaml
# GitHub Actions example
- name: Validate HuggingFace Token in Docker
  run: |
    ./scripts/validate-hf-token-docker.sh
  env:
    HUGGINGFACE_TOKEN: ${{ secrets.HUGGINGFACE_TOKEN }}
```

### Docker Compose Integration
```yaml
# Add to docker-compose.yml
services:
  token-validation:
    build:
      context: .
      dockerfile: docker/Dockerfile.validation
    environment:
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
    profiles:
      - validation
```

## 📁 File Structure

```
├── docker/
│   └── Dockerfile.validation          # Validation container definition
├── scripts/
│   ├── validate-hf-token-docker.sh    # Main validation script
│   ├── validate-hf-token-docker.py    # Python validation logic
│   └── docker-validation-startup.sh   # Container startup script
├── docker-compose.validation.yml      # Docker Compose for validation
└── docs/
    └── DOCKER_VALIDATION_GUIDE.md     # This guide
```

## 🎉 Benefits of Docker Validation

### **Reliability**
- Tests in exact deployment environment
- Eliminates host system differences
- Catches environment-specific issues

### **Accuracy**
- Same Python version and dependencies
- Same network and security context
- Same file permissions and user setup

### **Debugging**
- Isolates token issues from environment issues
- Provides detailed error messages
- Matches production error conditions

### **Confidence**
- If validation passes in Docker, deployment will work
- Eliminates "works on my machine" problems
- Provides production-ready validation

## 🆘 Getting Help

### Common Issues and Solutions

1. **Permission Denied Errors**
   - ✅ Fixed automatically by Docker validation
   - Uses temporary directories
   - Runs as proper user context

2. **Token Format Issues**
   - Check token starts with `hf_`
   - Ensure exactly 37 characters
   - Verify no extra spaces or quotes

3. **License Not Accepted**
   - Visit: https://huggingface.co/google/medgemma-4b-it
   - Accept the license agreement
   - Wait 5-10 minutes for propagation

4. **Network Connectivity**
   - Check Docker can reach internet
   - Verify firewall settings
   - Test with: `docker run --rm alpine ping huggingface.co`

### Support Resources
- **Docker Documentation**: https://docs.docker.com
- **HuggingFace Hub**: https://huggingface.co/docs/huggingface_hub
- **MedGemma Model**: https://huggingface.co/google/medgemma-4b-it

The Docker-based validation ensures that your token will work in the exact same environment where MedGemma will be deployed, eliminating environment-related authentication issues.
