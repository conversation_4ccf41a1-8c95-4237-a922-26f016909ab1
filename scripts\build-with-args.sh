#!/bin/bash
# Build script using Docker build arguments (ALTERNATIVE APPROACH)
# Less secure than secrets but simpler to use

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with colors
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to get Hugging<PERSON>ace token
get_huggingface_token() {
    local token=""
    
    # Try to read from .hf_token file
    if [ -f ".hf_token" ]; then
        token=$(cat .hf_token | tr -d '\n\r')
        if [ -n "$token" ]; then
            log "Using HuggingFace token from .hf_token file"
            echo "$token"
            return 0
        fi
    fi
    
    # Try environment variable
    if [ -n "$HUGGINGFACE_TOKEN" ]; then
        log "Using HuggingFace token from environment variable"
        echo "$HUGGINGFACE_TOKEN"
        return 0
    fi
    
    # Prompt user for token
    warn "No HuggingFace token found in .hf_token file or HUGGINGFACE_TOKEN environment variable"
    echo
    echo "Please provide your HuggingFace token:"
    echo "1. Get your token from: https://huggingface.co/settings/tokens"
    echo "2. Accept the MedGemma license at: https://huggingface.co/google/medgemma-4b-it"
    echo
    read -p "Enter your HuggingFace token: " token
    
    if [ -z "$token" ]; then
        error "No token provided"
        exit 1
    fi
    
    echo "$token"
}

# Function to build the Docker image
build_image() {
    local image_name="${1:-medgemma-api}"
    local dockerfile="${2:-docker/Dockerfile.medgemma.buildarg}"
    local token="$3"
    
    log "Building Docker image: $image_name"
    log "Using Dockerfile: $dockerfile"
    
    warn "Using build arguments for HuggingFace token (less secure than secrets)"
    warn "The token will be visible in build logs and docker history"
    
    # Build with build arguments
    if docker build \
        --build-arg HUGGINGFACE_TOKEN="$token" \
        --build-arg HF_TOKEN="$token" \
        -f "$dockerfile" \
        -t "$image_name:latest" \
        -t "$image_name:$(date +%Y%m%d-%H%M%S)" \
        .; then
        success "Docker image built successfully: $image_name"
    else
        error "Docker build failed"
        exit 1
    fi
}

# Function to verify the build
verify_build() {
    local image_name="${1:-medgemma-api}"
    
    log "Verifying build..."
    
    # Check if image exists
    if docker images "$image_name:latest" | grep -q "$image_name"; then
        success "Image verification passed"
        
        # Show image size
        local size=$(docker images "$image_name:latest" --format "table {{.Size}}" | tail -n 1)
        log "Image size: $size"
    else
        error "Image verification failed"
        exit 1
    fi
}

# Function to clean up
cleanup() {
    log "Cleaning up..."
    
    # Remove intermediate images
    docker image prune -f >/dev/null 2>&1 || true
    
    success "Cleanup completed"
}

# Function to show security warning
show_security_warning() {
    echo
    warn "SECURITY WARNING:"
    warn "Build arguments are less secure than Docker secrets because:"
    warn "1. They appear in docker history"
    warn "2. They may be visible in build logs"
    warn "3. They could be exposed in CI/CD systems"
    echo
    warn "For production use, consider using the BuildKit secrets approach:"
    warn "Run: ./scripts/build-with-secrets.sh"
    echo
    read -p "Do you want to continue with build arguments? (y/n): " continue_build
    
    if [ "$continue_build" != "y" ] && [ "$continue_build" != "Y" ]; then
        log "Build cancelled by user"
        exit 0
    fi
}

# Main function
main() {
    echo "=== Docker Build with Build Arguments ==="
    echo "This script builds the MedGemma Docker image using build arguments"
    echo "for HuggingFace token handling during build."
    echo
    
    # Parse command line arguments
    local image_name="${1:-medgemma-api}"
    local dockerfile="${2:-docker/Dockerfile.medgemma.buildarg}"
    
    # Show security warning
    show_security_warning
    
    # Get HuggingFace token
    local token
    token=$(get_huggingface_token)
    
    # Run build process
    build_image "$image_name" "$dockerfile" "$token"
    verify_build "$image_name"
    cleanup
    
    echo
    success "Build completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Test the image: docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN='$token' $image_name"
    echo "2. Or use docker-compose: docker-compose -f docker-compose.dev.yml up"
    echo
    warn "Remember to set HUGGINGFACE_TOKEN environment variable when running the container"
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [image_name] [dockerfile]"
    echo
    echo "Arguments:"
    echo "  image_name  Name for the Docker image (default: medgemma-api)"
    echo "  dockerfile  Path to Dockerfile (default: docker/Dockerfile.medgemma.buildarg)"
    echo
    echo "Examples:"
    echo "  $0                                    # Use defaults"
    echo "  $0 my-medgemma                       # Custom image name"
    echo "  $0 my-medgemma docker/Dockerfile.custom  # Custom name and dockerfile"
    echo
    echo "Environment Variables:"
    echo "  HUGGINGFACE_TOKEN  Your HuggingFace token (alternative to .hf_token file)"
    exit 0
fi

# Run main function
main "$@"
