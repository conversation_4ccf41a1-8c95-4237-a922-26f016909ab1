#!/usr/bin/env python3

"""
Test script to verify the CORS_ORIGINS configuration fix
"""

import os
import sys
import tempfile

def test_cors_config():
    """Test different CORS_ORIGINS configurations"""

    print("Testing CORS_ORIGINS configurations after Pydantic v2 fix...")
    print("=" * 60)

    # Test cases that should work with the fix
    test_cases = [
        ("*", "Single asterisk (Phase 1 permissive case)"),
        ("http://localhost", "Single URL"),
        ("http://localhost,https://localhost", "Comma-separated values"),
        ("https://example.com,https://www.example.com", "Production URLs"),
        ("", "Empty string (should use defaults)"),
    ]

    success_count = 0
    total_count = len(test_cases)

    for cors_value, description in test_cases:
        print(f"\nTesting: {description}")
        print(f"Value: '{cors_value}'")

        # Create a temporary .env file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write(f"CORS_ORIGINS={cors_value}\n")
            f.write("API_KEY=test-api-key-32-characters-long\n")
            f.write("HUGGINGFACE_TOKEN=hf_test_token\n")
            temp_env_file = f.name

        try:
            # Set environment to use our test file
            os.environ['CORS_ORIGINS'] = cors_value

            # Import and test
            sys.path.insert(0, os.path.dirname(__file__))

            # Clear any cached modules
            modules_to_clear = [m for m in sys.modules.keys() if m.startswith('app.')]
            for module in modules_to_clear:
                del sys.modules[module]

            from app.config import Settings

            # Create settings instance
            settings = Settings()

            print(f"✅ SUCCESS: {settings.CORS_ORIGINS}")
            print(f"   Type: {type(settings.CORS_ORIGINS)}")
            success_count += 1

        except Exception as e:
            print(f"❌ ERROR: {e}")
            print(f"   Error type: {type(e).__name__}")

        finally:
            # Clean up
            if 'CORS_ORIGINS' in os.environ:
                del os.environ['CORS_ORIGINS']

            # Remove temp file
            try:
                os.unlink(temp_env_file)
            except:
                pass

    print(f"\n{'='*60}")
    print(f"Test Results: {success_count}/{total_count} passed")

    if success_count == total_count:
        print("🎉 All tests passed! The CORS_ORIGINS fix is working correctly.")
        return True
    else:
        print("💥 Some tests failed. The fix may need additional work.")
        return False

def test_phase1_specific():
    """Test the specific Phase 1 configuration"""
    print(f"\n{'='*60}")
    print("Testing Phase 1 specific configuration...")

    # Simulate Phase 1 environment
    phase1_env = {
        'CORS_ORIGINS': '*',
        'ALLOWED_HOSTS': '*',
        'API_KEY': 'your-super-secret-api-key-32-chars-minimum-change-this',
        'HUGGINGFACE_TOKEN': 'hf_your_token_here_replace_with_actual_token',
        'ENVIRONMENT': 'production'
    }

    try:
        # Set all Phase 1 environment variables
        for key, value in phase1_env.items():
            os.environ[key] = value

        # Clear cached modules
        modules_to_clear = [m for m in sys.modules.keys() if m.startswith('app.')]
        for module in modules_to_clear:
            del sys.modules[module]

        from app.config import Settings
        settings = Settings()

        print(f"✅ Phase 1 configuration loaded successfully!")
        print(f"   CORS_ORIGINS: {settings.CORS_ORIGINS}")
        print(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        print(f"   Environment: {settings.ENVIRONMENT}")

        return True

    except Exception as e:
        print(f"❌ Phase 1 configuration failed: {e}")
        return False

    finally:
        # Clean up environment
        for key in phase1_env.keys():
            if key in os.environ:
                del os.environ[key]

if __name__ == "__main__":
    basic_tests_passed = test_cors_config()
    phase1_test_passed = test_phase1_specific()

    if basic_tests_passed and phase1_test_passed:
        print(f"\n🎉 All tests passed! The Pydantic configuration fix is working.")
        print("✅ Phase 1 deployment should now work without JSON parsing errors.")
        sys.exit(0)
    else:
        print(f"\n💥 Some tests failed. Please review the configuration.")
        sys.exit(1)
