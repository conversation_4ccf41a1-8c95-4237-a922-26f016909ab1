#!/bin/bash
# EC2 Deployment Script for MedGemma FastAPI
# This script helps deploy MedGemma on AWS EC2 instances

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with colors
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to check if running on EC2
check_ec2_environment() {
    log "Checking if running on EC2..."
    
    if curl -s --connect-timeout 2 http://***************/latest/meta-data/instance-id >/dev/null 2>&1; then
        local instance_id=$(curl -s http://***************/latest/meta-data/instance-id)
        local instance_type=$(curl -s http://***************/latest/meta-data/instance-type)
        local region=$(curl -s http://***************/latest/meta-data/placement/region)
        
        success "Running on EC2 instance: $instance_id"
        log "Instance type: $instance_type"
        log "Region: $region"
        
        # Check if instance type is suitable
        case $instance_type in
            t3.xlarge|t3.2xlarge|m5.xlarge|m5.2xlarge|c5.xlarge|c5.2xlarge|r5.xlarge|r5.2xlarge)
                success "Instance type $instance_type is suitable for MedGemma"
                ;;
            t3.large|m5.large|c5.large|r5.large)
                warn "Instance type $instance_type may have limited memory for MedGemma"
                warn "Consider upgrading to xlarge or larger for better performance"
                ;;
            *)
                warn "Instance type $instance_type may not be optimal for MedGemma"
                warn "Recommended: t3.xlarge, m5.xlarge, c5.xlarge, or r5.xlarge"
                ;;
        esac
        
        return 0
    else
        warn "Not running on EC2 or metadata service unavailable"
        return 1
    fi
}

# Function to install Docker if not present
install_docker() {
    if command -v docker >/dev/null 2>&1; then
        log "Docker is already installed"
        return 0
    fi
    
    log "Installing Docker..."
    
    # Update package index
    sudo apt-get update
    
    # Install required packages
    sudo apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Set up stable repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker Engine
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Add current user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    success "Docker installed successfully"
    warn "Please log out and log back in for Docker group changes to take effect"
}

# Function to install Docker Compose if not present
install_docker_compose() {
    if command -v docker-compose >/dev/null 2>&1; then
        log "Docker Compose is already installed"
        return 0
    fi
    
    log "Installing Docker Compose..."
    
    # Download and install Docker Compose
    local compose_version="2.24.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/v${compose_version}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    success "Docker Compose installed successfully"
}

# Function to secure the .env file
secure_env_file() {
    local env_file="$1"

    if [ -f "$env_file" ]; then
        # Set restrictive permissions (owner read/write only)
        chmod 600 "$env_file"
        log "Set secure permissions (600) on $env_file"

        # Ensure .env is in .gitignore
        if [ -f ".gitignore" ]; then
            if ! grep -q "^\.env$" .gitignore; then
                echo ".env" >> .gitignore
                log "Added .env to .gitignore"
            fi
        else
            echo ".env" > .gitignore
            log "Created .gitignore with .env entry"
        fi

        success "Environment file secured"
    else
        warn "Environment file $env_file not found"
    fi
}

# Function to collect configuration
collect_configuration() {
    log "Collecting deployment configuration..."

    # HuggingFace token
    while true; do
        read -p "Enter your HuggingFace token (starts with hf_): " HUGGINGFACE_TOKEN
        if [[ "$HUGGINGFACE_TOKEN" =~ ^hf_ ]]; then
            break
        else
            warn "Token should start with 'hf_'. Please check your token."
        fi
    done

    # API key
    read -p "Enter API key for the service (leave empty for auto-generated): " API_KEY
    if [ -z "$API_KEY" ]; then
        API_KEY="medgemma-api-key-$(date +%s)"
        log "Generated API key: $API_KEY"
    fi

    # Redis password
    read -p "Enter Redis password (leave empty for auto-generated): " REDIS_PASSWORD
    if [ -z "$REDIS_PASSWORD" ]; then
        REDIS_PASSWORD="redis-$(openssl rand -hex 16)"
        log "Generated Redis password"
    fi

    # Domain configuration
    read -p "Enter domain name (leave empty for IP-based access): " DOMAIN_NAME

    success "Configuration collected successfully"
}

# Function to create environment file
create_environment_file() {
    log "Creating environment configuration..."

    cat > .env << EOF
# MedGemma EC2 Deployment Configuration
# Generated on $(date)
#
# SECURITY WARNING: This file contains sensitive information.
# - Keep file permissions restrictive (600)
# - Do not commit to version control
# - Do not share or expose publicly

# API Configuration
API_KEY=$API_KEY
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# Model Configuration
MODEL_NAME=google/medgemma-4b-it
MODEL_CACHE_DIR=/app/model_cache
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# HuggingFace Configuration (SENSITIVE)
HUGGINGFACE_TOKEN=$HUGGINGFACE_TOKEN
HF_TOKEN=$HUGGINGFACE_TOKEN

# Performance Configuration
WORKERS=1
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_DIR=/app/uploads
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff

# Security Configuration
CORS_ORIGINS=*
ALLOWED_HOSTS=*

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=$REDIS_PASSWORD

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_DIR=/app/logs

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Domain Configuration
DOMAIN_NAME=${DOMAIN_NAME:-localhost}

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=15
EOF

    # Secure the environment file
    secure_env_file ".env"

    success "Environment file created and secured: .env"
}

# Function to deploy the application
deploy_application() {
    log "Deploying MedGemma application..."
    
    # Build the Docker image
    log "Building Docker image..."
    docker build -f docker/Dockerfile.medgemma -t medgemma-api:latest .
    
    # Start the application with docker-compose
    log "Starting application with Docker Compose..."
    docker-compose -f docker-compose.yml up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 30
    
    # Check if services are running
    if docker-compose -f docker-compose.yml ps | grep -q "Up"; then
        success "Application deployed successfully!"
        
        # Get public IP
        local public_ip=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")
        
        echo
        echo "=== Deployment Complete ==="
        echo "Application URL: http://$public_ip:8000"
        echo "API Documentation: http://$public_ip:8000/docs"
        echo "Health Check: http://$public_ip:8000/health"
        echo "API Key: $API_KEY"
        echo
        echo "To check logs: docker-compose logs -f"
        echo "To stop: docker-compose down"
        echo
    else
        error "Deployment failed. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Main function
main() {
    echo "=== MedGemma EC2 Deployment Script ==="
    echo "This script will deploy MedGemma FastAPI on your EC2 instance"
    echo
    
    # Check environment
    check_ec2_environment

    # Install dependencies
    install_docker
    install_docker_compose

    # Collect configuration
    collect_configuration

    # Create environment file
    create_environment_file

    # Deploy application
    deploy_application

    success "EC2 deployment completed successfully!"
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0"
    echo
    echo "This script deploys MedGemma FastAPI on an EC2 instance."
    echo "It will:"
    echo "1. Check EC2 environment and instance suitability"
    echo "2. Install Docker and Docker Compose if needed"
    echo "3. Collect HuggingFace token and configuration"
    echo "4. Optionally store token in AWS Systems Manager"
    echo "5. Build and deploy the application"
    echo
    echo "Prerequisites:"
    echo "- EC2 instance with Ubuntu 20.04+ or Amazon Linux 2"
    echo "- At least 8GB RAM (t3.xlarge or larger recommended)"
    echo "- 20GB+ free disk space"
    echo "- Internet connectivity"
    echo "- HuggingFace token with MedGemma access"
    exit 0
fi

# Run main function
main "$@"
