# Multi-stage build for MedGemma FastAPI service - EC2 Optimized
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    HF_HOME=/app/model_cache \
    TRANSFORMERS_CACHE=/app/model_cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Upgrade pip and install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# Install specific transformers version for MedGemma compatibility
RUN pip install --no-cache-dir \
    "transformers>=4.50.0" \
    "torch>=2.0.0" \
    "torchvision>=0.15.0" \
    "torchaudio>=2.0.0" \
    --index-url https://download.pytorch.org/whl/cpu

# Install additional ML dependencies
RUN pip install --no-cache-dir \
    accelerate \
    bitsandbytes \
    optimum \
    "huggingface-hub>=0.20.0" \
    pillow \
    requests

# Copy application code and scripts
COPY app/ ./app/
COPY scripts/ ./scripts/

# Create necessary directories with proper permissions
RUN mkdir -p /app/model_cache /app/uploads /app/logs && \
    chown -R medgemma:medgemma /app && \
    chmod -R 755 /app

# Switch to non-root user
USER medgemma

# Create startup script that handles model download
COPY --chown=medgemma:medgemma scripts/ec2-startup.sh ./ec2-startup.sh
RUN chmod +x ./ec2-startup.sh

# Expose port
EXPOSE 8000

# Extended health check for model loading
HEALTHCHECK --interval=30s --timeout=15s --start-period=300s --retries=5 \
    CMD curl -f http://localhost:8000/health || exit 1

# Use startup script that handles model download and app startup
CMD ["./ec2-startup.sh"]
