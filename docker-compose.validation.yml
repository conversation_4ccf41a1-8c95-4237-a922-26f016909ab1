# Docker Compose for HuggingFace Token Validation
# Tests token in the same environment as MedGemma deployment

version: '3.8'

services:
  hf-token-validation:
    build:
      context: .
      dockerfile: docker/Dockerfile.validation
    container_name: medgemma-token-validation
    
    environment:
      # HuggingFace token (required)
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HF_TOKEN}
      
      # Cache configuration (same as deployment)
      - HF_HOME=/app/model_cache
      - TRANSFORMERS_CACHE=/app/model_cache
      - HF_HUB_CACHE=/app/model_cache
      
      # Model configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      
    volumes:
      # Optional: Mount validation logs
      - ./data/validation:/app/validation/logs
      
    # Remove container after validation
    restart: "no"
    
    # Network configuration (same as deployment)
    networks:
      - validation-network

networks:
  validation-network:
    driver: bridge
