# MedGemma AI Chat Application Configuration
# Copy this file to .env and update the values
#
# SECURITY WARNING: Never commit .env files to version control!
# After creating .env, set secure permissions: chmod 600 .env

# =============================================================================
# API Configuration
# =============================================================================
API_KEY=your-secret-api-key-change-this-in-production
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# =============================================================================
# Model Configuration
# =============================================================================
MODEL_NAME=google/medgemma-4b-it
MODEL_CACHE_DIR=/app/model_cache
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# =============================================================================
# HuggingFace Configuration (REQUIRED)
# =============================================================================
# Get your token from: https://huggingface.co/settings/tokens
# Accept MedGemma license: https://huggingface.co/google/medgemma-4b-it
# Token format should be: hf_abcdef1234567890...
HUGGINGFACE_TOKEN=hf_your_huggingface_token_here
HF_TOKEN=hf_your_huggingface_token_here

# =============================================================================
# Performance Configuration
# =============================================================================
WORKERS=1
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# =============================================================================
# File Upload Configuration
# =============================================================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_DIR=/app/uploads
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff

# =============================================================================
# Security Configuration
# =============================================================================
CORS_ORIGINS=http://localhost,https://localhost,https://your-domain.com
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# =============================================================================
# Database Configuration (Redis)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your-redis-password-here
CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# =============================================================================
# Logging Configuration
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_DIR=/app/logs

# =============================================================================
# Monitoring Configuration
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# SSL Configuration
# =============================================================================
DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>
SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem

# =============================================================================
# Health Check Configuration
# =============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# Docker Configuration
# =============================================================================
COMPOSE_PROJECT_NAME=medgemma-chat

# =============================================================================
# EC2 Deployment Notes
# =============================================================================
# For EC2 deployment:
# 1. Copy this file to .env: cp .env.example .env
# 2. Edit .env and add your actual HuggingFace token
# 3. Secure the file: chmod 600 .env
# 4. Ensure .env is in .gitignore
# 5. Deploy with: ./scripts/deploy-ec2.sh

# =============================================================================
# Backup Configuration
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
