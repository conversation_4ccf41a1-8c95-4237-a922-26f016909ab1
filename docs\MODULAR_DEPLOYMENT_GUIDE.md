# MedGemma AI Chat - Comprehensive Modular Deployment Guide

## 🎯 Overview

This guide provides a **fully modular, incremental deployment strategy** for a production-ready AI chat application using Google's MedGemma-4b-it model. Each phase is completely self-contained and functional, allowing you to stop at any phase and resume later.

## 🏗️ Architecture Overview

```
Phase 1: Core API Only          Phase 2: + Nginx Proxy         Phase 3: + SSL/TLS            Phase 4: + Advanced Features
┌─────────────────────┐        ┌─────────────────────┐        ┌─────────────────────┐       ┌─────────────────────┐
│   FastAPI (8000)    │        │   Nginx (80)        │        │   Nginx (443)       │       │   Nginx (443)       │
│   ├─ Chat API       │   →    │   ├─ Reverse Proxy  │   →    │   ├─ SSL Termination│  →   │   ├─ Rate Limiting   │
│   ├─ Image Upload   │        │   └─ Static Files   │        │   ├─ Auto Renewal   │       │   ├─ Security Headers│
│   ├─ Conversation   │        │                     │        │   └─ HTTPS Redirect │       │   └─ Advanced Logs  │
│   └─ Health Check   │        │   FastAPI (8000)    │        │                     │       │                     │
│                     │        │   ├─ Chat API       │        │   FastAPI (8000)    │       │   FastAPI (8000)    │
│   Redis (6379)      │        │   ├─ Image Upload   │        │   ├─ Chat API       │       │   ├─ Chat API       │
│   └─ Conversations  │        │   ├─ Conversation   │        │   ├─ Image Upload   │       │   ├─ Image Upload   │
│                     │        │   └─ Health Check   │        │   ├─ Conversation   │       │   ├─ Conversation   │
│                     │        │                     │        │   └─ Health Check   │       │   └─ Health Check   │
│                     │        │   Redis (6379)      │        │                     │       │                     │
│                     │        │   └─ Conversations  │        │   Redis (6379)      │       │   Redis (6379)      │
│                     │        │                     │        │   └─ Conversations  │       │   └─ Conversations  │
│                     │        │                     │        │                     │       │                     │
│                     │        │                     │        │   Certbot           │       │   Monitoring        │
│                     │        │                     │        │   └─ Auto SSL       │       │   ├─ Prometheus      │
│                     │        │                     │        │                     │       │   ├─ Grafana        │
│                     │        │                     │        │                     │       │   └─ Alerting       │
└─────────────────────┘        └─────────────────────┘        └─────────────────────┘       └─────────────────────┘

Direct API Access              HTTP Proxy Access              HTTPS Secure Access           Enterprise Production
✅ Fully Functional            ✅ Fully Functional            ✅ Fully Functional           ✅ Fully Functional
```

## 🚀 Quick Start Decision Tree

**Choose your deployment phase based on your needs:**

- **Phase 1 Only**: Need a working AI chat API for development/testing
- **Phase 2**: Need professional reverse proxy setup for staging/production
- **Phase 3**: Need secure HTTPS for production deployment
- **Phase 4**: Need enterprise-grade monitoring and security

## 📋 Prerequisites

### System Requirements
- **Target**: AWS EC2 t3.xlarge (4 vCPU, 16 GB RAM)
- **OS**: Ubuntu 24.04 LTS
- **Storage**: 50GB+ EBS storage
- **Network**: Dynamic IP support (no static IP required)

### Required Accounts & Tokens
- **HuggingFace Account**: With MedGemma-4b-it model access
- **HuggingFace Token**: For model authentication
- **AWS Account**: For EC2 deployment

### Local Requirements
- SSH client for server access
- Git (optional, for cloning repository)

---

## 🎯 Phase 1: Standalone Core Model & API

**✅ What You Get**: Fully functional AI chat API with direct access on port 8000

### Features Included
- ✅ Complete MedGemma-4b-it model integration
- ✅ Text-based chat conversations with streaming
- ✅ Image upload and analysis capabilities
- ✅ Conversation memory and session management
- ✅ Health monitoring and logging
- ✅ Basic HTML frontend for immediate testing
- ✅ Redis-based conversation storage
- ✅ API authentication and rate limiting

### Phase 1 Setup

#### 1.1 EC2 Instance Preparation

```bash
# Connect to your EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes
exit
# SSH back in
```

#### 1.2 Deploy Application

```bash
# Clone repository (or upload files)
git clone https://github.com/your-username/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Create Phase 1 environment configuration
cp .env.phase1.example .env.phase1
nano .env.phase1
```

**Phase 1 Environment Configuration (.env.phase1):**
```bash
# API Configuration
API_KEY=your-super-secret-api-key-32-chars-minimum
LOG_LEVEL=INFO
DEBUG=false

# Model Configuration
MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9
WORKERS=1

# HuggingFace Authentication (REQUIRED)
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here

# Network Configuration
HOST=0.0.0.0
PORT=8000
CORS_ORIGINS=*
ALLOWED_HOSTS=*

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760  # 10MB

# Conversation Management
CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400  # 24 hours

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password-here

# Security (Basic for Phase 1)
ENVIRONMENT=production
```

#### 1.3 Start Phase 1 Services

```bash
# Start Phase 1 deployment
docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d

# Monitor startup (model download may take 10-15 minutes)
docker-compose -f docker-compose.phase1.yml logs -f medgemma-api

# Check service status
docker-compose -f docker-compose.phase1.yml ps
```

#### 1.4 Verify Phase 1 Deployment

```bash
# Get your EC2 public IP
EC2_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
echo "Your API is available at: http://$EC2_IP:8000"

# Test health endpoint
curl http://$EC2_IP:8000/health

# Test chat endpoint
curl -X POST http://$EC2_IP:8000/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"message": "Hello, can you help me with medical questions?"}'

# Access web interface
echo "Web interface: http://$EC2_IP:8000"
```

### Phase 1 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Basic web interface |
| `/health` | GET | Health check |
| `/docs` | GET | API documentation |
| `/chat` | POST | Text-based chat |
| `/analyze-image` | POST | Image analysis |
| `/conversations/{id}` | GET | Get conversation history |
| `/conversations/{id}` | DELETE | Delete conversation |
| `/metrics` | GET | Basic metrics |

### Phase 1 Testing

```bash
# Run comprehensive Phase 1 tests
./scripts/test-phase1.sh

# Manual testing with included frontend
# Open browser to http://your-ec2-ip:8000
```

**✅ Phase 1 Complete!** 
Your AI chat API is fully functional and accessible at `http://your-ec2-ip:8000`

**Exit Point**: You can stop here and have a complete working AI chat application.

---

## 🔄 Phase 2: Production Nginx Setup (Optional Enhancement)

**✅ What You Get**: Professional reverse proxy setup with HTTP access

### Additional Features
- ✅ Nginx reverse proxy for production-grade routing
- ✅ Static file serving optimization
- ✅ Request/response header management
- ✅ Basic rate limiting and security headers
- ✅ Centralized logging
- ✅ Load balancing preparation
- ✅ All Phase 1 functionality preserved

### Phase 2 Migration

#### 2.1 Prepare Phase 2 Configuration

```bash
# Create Phase 2 environment (extends Phase 1)
cp .env.phase1 .env.phase2
echo "" >> .env.phase2
echo "# Phase 2: Nginx Configuration" >> .env.phase2
echo "DOMAIN_NAME=your-ec2-ip-or-domain.com" >> .env.phase2
echo "NGINX_CLIENT_MAX_BODY_SIZE=10m" >> .env.phase2
echo "NGINX_WORKER_PROCESSES=auto" >> .env.phase2

# Edit if you have a domain name
nano .env.phase2
```

#### 2.2 Deploy Phase 2 (Nginx + API)

```bash
# Stop Phase 1 services
docker-compose -f docker-compose.phase1.yml down

# Start Phase 2 services
docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d

# Monitor deployment
docker-compose -f docker-compose.phase2.yml logs -f
```

#### 2.3 Verify Phase 2 Deployment

```bash
# Test through Nginx (port 80)
curl http://$EC2_IP/api/health

# Test web interface through Nginx
curl http://$EC2_IP/

# Test API through Nginx
curl -X POST http://$EC2_IP/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"message": "Testing through Nginx proxy"}'
```

### Phase 2 Architecture

```
Internet → Nginx (Port 80) → FastAPI (Port 8000)
                ↓
           Static Files (Frontend)
```

**✅ Phase 2 Complete!** 
Your application now has professional reverse proxy setup accessible at `http://your-ec2-ip`

**Exit Point**: You can stop here and have a production-ready HTTP deployment.

---

## 🔒 Phase 3: SSL/TLS Security (Optional Enhancement)

**✅ What You Get**: Secure HTTPS deployment with automatic certificate management

### Additional Features
- ✅ SSL/TLS encryption with Let's Encrypt
- ✅ Automatic certificate renewal
- ✅ HTTPS redirect from HTTP
- ✅ Security headers (HSTS, CSP, etc.)
- ✅ Dynamic IP certificate management
- ✅ All Phase 1 & 2 functionality preserved

### Phase 3 Migration

#### 3.1 Domain Configuration (Optional)

```bash
# If you have a domain name, configure DNS
# Point your domain to your EC2 IP address
# A record: your-domain.com → your-ec2-ip
# A record: www.your-domain.com → your-ec2-ip

# Update Phase 3 environment
cp .env.phase2 .env.phase3
echo "" >> .env.phase3
echo "# Phase 3: SSL Configuration" >> .env.phase3
echo "SSL_EMAIL=<EMAIL>" >> .env.phase3
echo "DOMAIN_NAME=your-domain.com" >> .env.phase3  # or use EC2 IP
echo "CERTBOT_STAGING=false" >> .env.phase3  # Set to true for testing

nano .env.phase3
```

#### 3.2 Deploy Phase 3 (HTTPS)

```bash
# Stop Phase 2 services
docker-compose -f docker-compose.phase2.yml down

# Start Phase 3 services
docker-compose -f docker-compose.phase3.yml --env-file .env.phase3 up -d

# Wait for services to start
sleep 30

# Generate SSL certificate
./scripts/setup-ssl-phase3.sh
```

#### 3.3 Verify Phase 3 Deployment

```bash
# Test HTTPS endpoint
curl -k https://$EC2_IP/api/health

# Test HTTP to HTTPS redirect
curl -I http://$EC2_IP/

# Test secure web interface
echo "Secure web interface: https://$EC2_IP"
```

### Phase 3 Architecture

```
Internet → Nginx (Port 443 HTTPS) → FastAPI (Port 8000)
              ↓
         SSL Termination
              ↓
         Security Headers
              ↓
         Static Files (Frontend)
```

**✅ Phase 3 Complete!** 
Your application now has enterprise-grade HTTPS security accessible at `https://your-domain-or-ip`

**Exit Point**: You can stop here and have a fully secured production deployment.

---

## 🚀 Phase 4: Advanced Production Features (Optional Enhancement)

**✅ What You Get**: Enterprise-grade production deployment with monitoring and advanced security

### Additional Features
- ✅ Prometheus monitoring and metrics
- ✅ Grafana dashboards
- ✅ Advanced logging and alerting
- ✅ Performance optimization
- ✅ Enhanced security hardening
- ✅ Backup and recovery automation
- ✅ All previous phase functionality preserved

### Phase 4 Migration

#### 4.1 Prepare Phase 4 Configuration

```bash
# Create Phase 4 environment (extends Phase 3)
cp .env.phase3 .env.phase4
echo "" >> .env.phase4
echo "# Phase 4: Advanced Features" >> .env.phase4
echo "GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password" >> .env.phase4
echo "PROMETHEUS_RETENTION=30d" >> .env.phase4
echo "BACKUP_ENABLED=true" >> .env.phase4
echo "BACKUP_SCHEDULE=0 2 * * *" >> .env.phase4

nano .env.phase4
```

#### 4.2 Deploy Phase 4 (Full Production)

```bash
# Stop Phase 3 services
docker-compose -f docker-compose.phase3.yml down

# Start Phase 4 services
docker-compose -f docker-compose.phase4.yml --env-file .env.phase4 up -d

# Monitor deployment
docker-compose -f docker-compose.phase4.yml logs -f
```

#### 4.3 Verify Phase 4 Deployment

```bash
# Test main application
curl -k https://$EC2_IP/api/health

# Test monitoring endpoints
curl -k https://$EC2_IP/metrics
echo "Grafana dashboard: https://$EC2_IP:3000"
echo "Prometheus: https://$EC2_IP:9090"
```

### Phase 4 Architecture

```
Internet → Nginx (Port 443 HTTPS) → FastAPI (Port 8000)
              ↓                           ↓
         SSL Termination              Prometheus Metrics
              ↓                           ↓
         Security Headers            Grafana Dashboard
              ↓                           ↓
         Rate Limiting               Alerting System
              ↓
         Static Files (Frontend)
```

**✅ Phase 4 Complete!**
You now have an enterprise-grade production deployment with full monitoring and security.

---

## 🔄 Migration Between Phases

### Upgrading to Next Phase

```bash
# General upgrade pattern
docker-compose -f docker-compose.phase[N].yml down
docker-compose -f docker-compose.phase[N+1].yml --env-file .env.phase[N+1] up -d
```

### Downgrading to Previous Phase

```bash
# General downgrade pattern
docker-compose -f docker-compose.phase[N].yml down
docker-compose -f docker-compose.phase[N-1].yml --env-file .env.phase[N-1] up -d
```

### Data Persistence

All phases preserve:
- ✅ Model cache (no re-download needed)
- ✅ Conversation history
- ✅ Uploaded images
- ✅ Configuration settings

---

## 📊 Monitoring and Maintenance

### Health Checks (All Phases)

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Monitor resources
docker stats

# Test API health
curl http://your-endpoint/health
```

### Backup Procedures (Phase 4)

```bash
# Manual backup
./scripts/backup-all-phases.sh

# Restore from backup
./scripts/restore-backup.sh backup-file.tar.gz
```

---

## 🚨 Troubleshooting

### Common Issues Across All Phases

1. **Model Download Issues**
   ```bash
   # Check HuggingFace token
   docker-compose logs medgemma-api | grep -i "token\|auth"
   ```

2. **Memory Issues**
   ```bash
   # Monitor memory usage
   free -h
   docker stats
   ```

3. **Network Connectivity**
   ```bash
   # Check port accessibility
   netstat -tlnp | grep :8000
   ```

### Phase-Specific Troubleshooting

- **Phase 1**: Direct API access issues
- **Phase 2**: Nginx proxy configuration
- **Phase 3**: SSL certificate problems
- **Phase 4**: Monitoring service issues

---

## 📞 Support and Documentation

### Additional Resources
- [API Documentation](API.md)
- [Configuration Guide](CONFIGURATION.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Development Guide](DEVELOPMENT.md)

### Getting Help
- Check logs: `docker-compose logs -f`
- Review configuration files
- Test individual components
- Create GitHub issue with logs and configuration

---

## 🎉 Deployment Summary

| Phase | Access URL | Features | Use Case |
|-------|------------|----------|----------|
| **Phase 1** | `http://ip:8000` | Core API + Frontend | Development/Testing |
| **Phase 2** | `http://ip` | + Nginx Proxy | Staging/Production HTTP |
| **Phase 3** | `https://domain` | + SSL/TLS Security | Production HTTPS |
| **Phase 4** | `https://domain` | + Monitoring/Security | Enterprise Production |

**Choose the phase that meets your current needs - you can always upgrade later!**
