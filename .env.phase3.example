# Phase 3: SSL/TLS Security Configuration
# Extends Phase 2 with HTTPS encryption and security headers
# Copy this file to .env.phase3 and customize the values

# =============================================================================
# API CONFIGURATION (from Phase 1 & 2)
# =============================================================================

API_KEY=your-super-secret-api-key-32-chars-minimum-change-this
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=production

# =============================================================================
# MODEL CONFIGURATION (from Phase 1 & 2)
# =============================================================================

MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9
WORKERS=1

# =============================================================================
# HUGGINGFACE AUTHENTICATION (from Phase 1 & 2)
# =============================================================================

HUGGINGFACE_TOKEN=hf_your_token_here_replace_with_actual_token
HF_TOKEN=hf_your_token_here_replace_with_actual_token

# =============================================================================
# DOMAIN CONFIGURATION (REQUIRED for Phase 3)
# =============================================================================

# Domain name (REQUIRED for SSL certificates)
# Must be a valid domain pointing to your EC2 instance
DOMAIN_NAME=your-domain.com

# Email for SSL certificate registration (REQUIRED)
SSL_EMAIL=<EMAIL>

# =============================================================================
# SSL/TLS CONFIGURATION (New for Phase 3)
# =============================================================================

# Certbot staging mode (set to true for testing)
CERTBOT_STAGING=false

# =============================================================================
# NETWORK CONFIGURATION (Updated for HTTPS)
# =============================================================================

# CORS origins (updated for HTTPS)
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# =============================================================================
# NGINX CONFIGURATION (Updated for SSL)
# =============================================================================

NGINX_CLIENT_MAX_BODY_SIZE=10m
NGINX_WORKER_PROCESSES=auto

# =============================================================================
# FILE UPLOAD CONFIGURATION (from previous phases)
# =============================================================================

UPLOAD_MAX_SIZE=10485760

# =============================================================================
# CONVERSATION MANAGEMENT (from previous phases)
# =============================================================================

CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# =============================================================================
# REDIS CONFIGURATION (from previous phases)
# =============================================================================

REDIS_PASSWORD=your-secure-redis-password-change-this

# =============================================================================
# PHASE 3 SPECIFIC SETTINGS
# =============================================================================

# Phase 3 adds SSL/TLS encryption:
# - HTTPS on port 443
# - HTTP to HTTPS redirect
# - Let's Encrypt certificates
# - Security headers (HSTS, CSP, etc.)
# - Automatic certificate renewal

# =============================================================================
# DNS REQUIREMENTS FOR PHASE 3
# =============================================================================

# Before deploying Phase 3, ensure:
# 1. Domain name points to your EC2 public IP
# 2. A record: your-domain.com → your-ec2-ip
# 3. A record: www.your-domain.com → your-ec2-ip (optional)
# 4. DNS propagation is complete (check with: dig your-domain.com)

# =============================================================================
# MIGRATION FROM PHASE 2
# =============================================================================

# To migrate from Phase 2 to Phase 3:
# 1. Ensure DNS is configured properly
# 2. Stop Phase 2: docker-compose -f docker-compose.phase2.yml down
# 3. Copy data: ./scripts/migrate-phase2-to-phase3.sh
# 4. Update this config file with your domain
# 5. Deploy Phase 3: docker-compose -f docker-compose.phase3.yml --env-file .env.phase3 up -d
# 6. Generate SSL certificate: ./scripts/setup-ssl-phase3.sh

# =============================================================================
# DEPLOYMENT COMMANDS FOR PHASE 3
# =============================================================================

# After configuring this file:
# 1. Copy to .env.phase3: cp .env.phase3.example .env.phase3
# 2. Edit values: nano .env.phase3
# 3. Verify DNS: dig your-domain.com
# 4. Create data directories: ./scripts/setup-phase3-directories.sh
# 5. Deploy: docker-compose -f docker-compose.phase3.yml --env-file .env.phase3 up -d
# 6. Generate SSL: ./scripts/setup-ssl-phase3.sh
# 7. Monitor: docker-compose -f docker-compose.phase3.yml logs -f
# 8. Test: curl https://your-domain.com/api/health

# =============================================================================
# PHASE 3 BENEFITS
# =============================================================================

# ✅ SSL/TLS encryption with Let's Encrypt
# ✅ Automatic certificate renewal
# ✅ HTTPS redirect from HTTP
# ✅ Security headers (HSTS, CSP, etc.)
# ✅ Dynamic IP certificate management
# ✅ All Phase 1 & 2 functionality preserved

# =============================================================================
# TROUBLESHOOTING PHASE 3
# =============================================================================

# Common issues:
# 1. DNS not propagated: Wait 24-48 hours or check with DNS checker
# 2. Certificate generation fails: Check domain ownership and email
# 3. HTTPS not working: Verify certificate files and Nginx config

# =============================================================================
# NEXT STEPS
# =============================================================================

# Ready for Phase 4? Add enterprise monitoring:
# - Prometheus metrics
# - Grafana dashboards
# - Advanced logging and alerting
# - Backup automation
