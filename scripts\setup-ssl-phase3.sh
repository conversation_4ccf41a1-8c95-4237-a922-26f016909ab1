#!/bin/bash

# Phase 3 SSL Setup Script
# Generates SSL certificates using Let's Encrypt for Phase 3 deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load environment variables
if [[ -f ".env.phase3" ]]; then
    source .env.phase3
    print_status "Loaded .env.phase3 configuration"
else
    print_error ".env.phase3 file not found"
    print_error "Please create .env.phase3 before running SSL setup"
    exit 1
fi

# Validate required variables
if [[ -z "$DOMAIN_NAME" ]]; then
    print_error "DOMAIN_NAME not set in .env.phase3"
    exit 1
fi

if [[ -z "$SSL_EMAIL" ]]; then
    print_error "SSL_EMAIL not set in .env.phase3"
    exit 1
fi

print_status "🔒 Setting up SSL certificates for Phase 3..."
print_status "Domain: $DOMAIN_NAME"
print_status "Email: $SSL_EMAIL"

# Check if domain resolves to current server
print_status "Checking DNS resolution..."
DOMAIN_IP=$(dig +short "$DOMAIN_NAME" | tail -n1)
SERVER_IP=$(curl -s --connect-timeout 10 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "unknown")

if [[ -n "$DOMAIN_IP" && "$DOMAIN_IP" == "$SERVER_IP" ]]; then
    print_success "DNS resolution correct: $DOMAIN_NAME -> $DOMAIN_IP"
elif [[ -n "$DOMAIN_IP" ]]; then
    print_warning "DNS mismatch: $DOMAIN_NAME -> $DOMAIN_IP, Server IP: $SERVER_IP"
    print_warning "SSL certificate generation may fail if DNS is not correct"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    print_warning "Could not resolve domain: $DOMAIN_NAME"
    print_warning "Please ensure DNS is configured correctly"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if Phase 3 services are running
print_status "Checking Phase 3 services..."
if ! docker-compose -f docker-compose.phase3.yml ps | grep -q "Up"; then
    print_error "Phase 3 services are not running"
    print_status "Please start Phase 3 services first:"
    print_status "  docker-compose -f docker-compose.phase3.yml --env-file .env.phase3 up -d"
    exit 1
fi

# Wait for Nginx to be ready
print_status "Waiting for Nginx to be ready..."
sleep 10

# Test HTTP access for ACME challenge
print_status "Testing HTTP access for ACME challenge..."
if curl -s --connect-timeout 10 "http://$DOMAIN_NAME/.well-known/acme-challenge/test" | grep -q "404"; then
    print_success "HTTP access working for ACME challenge"
else
    print_warning "HTTP access test failed, but continuing..."
fi

# Determine certbot command based on staging flag
CERTBOT_STAGING_FLAG=""
if [[ "${CERTBOT_STAGING:-false}" == "true" ]]; then
    CERTBOT_STAGING_FLAG="--staging"
    print_warning "Using Let's Encrypt staging environment (test certificates)"
else
    print_status "Using Let's Encrypt production environment"
fi

# Generate SSL certificate
print_status "Generating SSL certificate..."

# Create certificate directory
mkdir -p data/phase3/ssl_certs
mkdir -p data/phase3/certbot_webroot

# Run certbot
CERTBOT_CMD="docker-compose -f docker-compose.phase3.yml run --rm certbot certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email $SSL_EMAIL \
    --agree-tos \
    --no-eff-email \
    $CERTBOT_STAGING_FLAG \
    -d $DOMAIN_NAME"

# Add www subdomain if not already specified
if [[ "$DOMAIN_NAME" != www.* ]]; then
    CERTBOT_CMD="$CERTBOT_CMD -d www.$DOMAIN_NAME"
fi

print_status "Running certbot command..."
print_status "Command: $CERTBOT_CMD"

if eval $CERTBOT_CMD; then
    print_success "SSL certificate generated successfully!"
else
    print_error "SSL certificate generation failed"
    print_status "Common issues and solutions:"
    print_status "1. DNS not pointing to this server"
    print_status "2. Port 80 not accessible from internet"
    print_status "3. Domain not properly configured"
    print_status "4. Rate limiting (try --staging flag first)"
    exit 1
fi

# Copy certificates to Nginx location
print_status "Configuring certificates for Nginx..."

CERT_DIR="data/phase3/ssl_certs/live/$DOMAIN_NAME"
NGINX_CERT_DIR="data/phase3/ssl_certs/live"

if [[ -d "$CERT_DIR" ]]; then
    # Create symlinks for Nginx
    mkdir -p "$NGINX_CERT_DIR"
    
    ln -sf "$CERT_DIR/fullchain.pem" "$NGINX_CERT_DIR/cert.pem"
    ln -sf "$CERT_DIR/privkey.pem" "$NGINX_CERT_DIR/privkey.pem"
    ln -sf "$CERT_DIR/chain.pem" "$NGINX_CERT_DIR/chain.pem"
    
    print_success "Certificate symlinks created for Nginx"
else
    print_error "Certificate directory not found: $CERT_DIR"
    exit 1
fi

# Restart Nginx to load certificates
print_status "Restarting Nginx to load SSL certificates..."
docker-compose -f docker-compose.phase3.yml restart nginx

# Wait for Nginx to restart
sleep 10

# Test HTTPS access
print_status "Testing HTTPS access..."
if curl -k -s --connect-timeout 10 "https://$DOMAIN_NAME/health" | grep -q "healthy"; then
    print_success "HTTPS access working!"
else
    print_warning "HTTPS access test failed, checking configuration..."
    
    # Check Nginx logs
    print_status "Nginx error logs:"
    docker-compose -f docker-compose.phase3.yml logs nginx | tail -20
fi

# Set up automatic renewal
print_status "Setting up automatic certificate renewal..."

# Create renewal script
cat > scripts/renew-ssl-phase3.sh << 'EOF'
#!/bin/bash

# SSL Certificate Renewal Script for Phase 3
# Run this script periodically to renew SSL certificates

set -e

echo "🔄 Renewing SSL certificates..."

# Load environment
if [[ -f ".env.phase3" ]]; then
    source .env.phase3
else
    echo "Error: .env.phase3 not found"
    exit 1
fi

# Renew certificates
docker-compose -f docker-compose.phase3.yml run --rm certbot renew

# Restart Nginx if certificates were renewed
if [[ $? -eq 0 ]]; then
    echo "✅ Certificates renewed, restarting Nginx..."
    docker-compose -f docker-compose.phase3.yml restart nginx
else
    echo "ℹ️ No certificates needed renewal"
fi

echo "🔒 SSL renewal check completed"
EOF

chmod +x scripts/renew-ssl-phase3.sh
print_success "Created SSL renewal script: scripts/renew-ssl-phase3.sh"

# Create cron job for automatic renewal
print_status "Setting up automatic renewal cron job..."

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "renew-ssl-phase3.sh"; then
    print_warning "SSL renewal cron job already exists"
else
    # Add cron job (run twice daily)
    (crontab -l 2>/dev/null; echo "0 2,14 * * * cd $(pwd) && ./scripts/renew-ssl-phase3.sh >> logs/ssl-renewal.log 2>&1") | crontab -
    print_success "Added SSL renewal cron job (runs twice daily)"
fi

# Test certificate validity
print_status "Testing certificate validity..."
if openssl s_client -connect "$DOMAIN_NAME:443" -servername "$DOMAIN_NAME" </dev/null 2>/dev/null | openssl x509 -noout -dates; then
    print_success "Certificate validity check passed"
else
    print_warning "Certificate validity check failed"
fi

# Display certificate information
print_status "Certificate information:"
echo "Domain: $DOMAIN_NAME"
echo "Certificate path: $CERT_DIR"
echo "Nginx certificate path: $NGINX_CERT_DIR"

if [[ "${CERTBOT_STAGING:-false}" == "true" ]]; then
    print_warning "⚠️  STAGING CERTIFICATES GENERATED"
    print_warning "These are test certificates and will show browser warnings"
    print_warning "To generate production certificates:"
    print_warning "1. Set CERTBOT_STAGING=false in .env.phase3"
    print_warning "2. Run this script again"
fi

# Final verification
print_status "🔍 Final verification..."

# Test HTTPS redirect
if curl -s -I "http://$DOMAIN_NAME" | grep -q "301"; then
    print_success "HTTP to HTTPS redirect working"
else
    print_warning "HTTP to HTTPS redirect not working properly"
fi

# Test HTTPS health endpoint
if curl -k -s "https://$DOMAIN_NAME/api/health" | grep -q "healthy"; then
    print_success "HTTPS API access working"
else
    print_warning "HTTPS API access not working properly"
fi

# Summary
echo ""
print_success "🎉 Phase 3 SSL setup completed!"
echo ""
print_status "🌐 Your application is now accessible via HTTPS:"
echo "  • Main site: https://$DOMAIN_NAME"
echo "  • API: https://$DOMAIN_NAME/api/"
echo "  • Health check: https://$DOMAIN_NAME/api/health"
echo ""
print_status "🔧 SSL management:"
echo "  • Manual renewal: ./scripts/renew-ssl-phase3.sh"
echo "  • Automatic renewal: Configured via cron (twice daily)"
echo "  • Certificate location: $CERT_DIR"
echo ""
print_status "🚀 Ready for Phase 4?"
echo "  • Phase 4 adds enterprise monitoring and advanced features"
echo "  • Run: ./scripts/setup-phase4-directories.sh"

print_success "Phase 3 SSL setup completed successfully!"
