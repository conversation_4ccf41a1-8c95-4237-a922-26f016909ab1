#!/bin/bash
# Hugging Face Model Download Script
# For gated model: google/medgemma-4b-it

# Exit on error and show commands
set -e

# Configuration
MODEL="google/medgemma-4b-it"
HF_CLI="huggingface-cli"
REQUIRED_PYTHON="3.7"

# Check Python version
python_version=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")' 2>/dev/null)
if [[ -z "$python_version" ]] || [[ "$(echo -e "${REQUIRED_PYTHON}\n${python_version}" | sort -V | head -n1)" != "$REQUIRED_PYTHON" ]]; then
    echo "❌ Python $REQUIRED_PYTHON+ is required. Detected Python $python_version"
    exit 1
fi

# Install/update huggingface_hub if needed
if ! pip show huggingface-hub >/dev/null 2>&1; then
    echo "Installing huggingface_hub library..."
    pip install -U huggingface_hub
fi

# Get token input
read -p "Enter your Hugging Face token: " HF_TOKEN
echo

# Validate token format
if [[ ! "$HF_TOKEN" =~ ^hf_ ]]; then
    echo "❌ Invalid token format: Hugging Face tokens must start with 'hf_'"
    exit 1
fi

# Set cache directory (override if needed)
export HF_HOME="${HF_HOME:-$HOME/.cache/huggingface}"

# Login with the token
echo "🔐 Authenticating with Hugging Face Hub..."
$HF_CLI login --token "$HF_TOKEN" >/dev/null 2>&1

# Verify authentication
echo "🔍 Verifying token validity..."
USER_INFO=$($HF_CLI whoami 2>/dev/null)
if [[ -z "$USER_INFO" ]]; then
    echo "❌ Token validation failed. Invalid token or network issue"
    exit 1
fi

echo "✅ Authentication successful! Logged in as: $(echo "$USER_INFO" | grep 'Name:' | cut -d' ' -f4-)"

# Check model access
echo "🔎 Checking access to $MODEL..."
if ! $HF_CLI repo-info $MODEL >/dev/null 2>&1; then
    echo "❌ You don't have access to $MODEL or it doesn't exist"
    echo "   Request access at: https://huggingface.co/$MODEL"
    exit 1
fi

# Download the model
echo "📥 Downloading model (this may take several minutes, size ~4GB)..."
$HF_CLI download $MODEL \
    --repo-type model \
    --local-dir ./medgemma-4b-it \
    --local-dir-use-symlinks False \
    --progress true \
    --resume-download true

echo ""
echo "🎉 Download complete! Model saved to: $(pwd)/medgemma-4b-it"
echo "💡 To use the model in Python:"
echo "   from transformers import AutoModel"
echo "   model = AutoModel.from_pretrained('$(pwd)/medgemma-4b-it')"