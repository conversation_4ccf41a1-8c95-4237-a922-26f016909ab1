#!/bin/bash

# Simple HuggingFace Token Validation Script
# Basic validation without Python dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${PURPLE}[STEP $1]${NC} $2"
}

# Configuration
MODEL_NAME="google/medgemma-4b-it"
HF_API_BASE="https://huggingface.co/api"
TEST_TIMEOUT=300

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              HuggingFace Token Validator                    ║"
    print_header "║                 MedGemma-4b-it Access                       ║"
    print_header "║                   (Simple Version)                          ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to get token from various sources
get_huggingface_token() {
    local token=""
    
    # Check command line argument first
    if [[ $# -gt 0 && -n "$1" ]]; then
        token="$1"
        print_status "Using token from command line argument"
        echo "$token"
        return 0
    fi
    
    # Check environment variable
    if [[ -n "$HUGGINGFACE_TOKEN" ]]; then
        token="$HUGGINGFACE_TOKEN"
        print_status "Using token from HUGGINGFACE_TOKEN environment variable"
        echo "$token"
        return 0
    fi
    
    # Check .env.phase1 file
    if [[ -f ".env.phase1" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env.phase1 | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            print_status "Using token from .env.phase1 file"
            echo "$token"
            return 0
        fi
    fi
    
    # Check .env file
    if [[ -f ".env" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            print_status "Using token from .env file"
            echo "$token"
            return 0
        fi
    fi
    
    # Check HF_TOKEN as fallback
    if [[ -n "$HF_TOKEN" ]]; then
        token="$HF_TOKEN"
        print_status "Using token from HF_TOKEN environment variable"
        echo "$token"
        return 0
    fi
    
    print_error "No HuggingFace token found!"
    return 1
}

# Function to validate token syntax
validate_token_syntax() {
    local token="$1"
    
    print_step "1" "Validating token syntax..."
    
    # Check if token is empty
    if [[ -z "$token" ]]; then
        print_error "Token is empty"
        return 1
    fi
    
    print_success "Token syntax is valid"
    return 0
}

# Function to test basic HuggingFace API authentication
test_hf_authentication() {
    local token="$1"
    
    print_step "2" "Testing HuggingFace API authentication..."
    print_status "Token: $token"
    
    # Test whoami endpoint
    local http_code
    http_code=$(curl -s -w "%{http_code}" -o /tmp/hf_auth_response \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "$HF_API_BASE/whoami" 2>/dev/null)
    
    case $http_code in
        200)
            print_success "Authentication successful"
            # Try to extract username if possible
            if [[ -f "/tmp/hf_auth_response" ]]; then
                local username=$(grep -o '"name":"[^"]*"' /tmp/hf_auth_response 2>/dev/null | cut -d'"' -f4 || echo "Unknown")
                if [[ "$username" != "Unknown" && -n "$username" ]]; then
                    print_status "Authenticated as: $username"
                fi
            fi
            return 0
            ;;
        401)
            print_error "Authentication failed - Invalid token"
            print_status "The token is not recognized by HuggingFace"
            return 1
            ;;
        403)
            print_error "Authentication failed - Token permissions insufficient"
            print_status "The token exists but lacks necessary permissions"
            return 1
            ;;
        *)
            print_error "Authentication test failed - HTTP $http_code"
            if [[ -f "/tmp/hf_auth_response" ]]; then
                print_status "Response: $(cat /tmp/hf_auth_response | head -c 200)"
            fi
            return 1
            ;;
    esac
}

# Function to test model access
test_model_access() {
    local token="$1"
    
    print_step "3" "Testing access to $MODEL_NAME..."
    
    # Test model info endpoint
    local http_code
    http_code=$(curl -s -w "%{http_code}" -o /tmp/hf_model_response \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "$HF_API_BASE/models/$MODEL_NAME" 2>/dev/null)
    
    case $http_code in
        200)
            print_success "Model access successful"
            
            # Try to extract basic model information
            if [[ -f "/tmp/hf_model_response" ]]; then
                # Check if model is gated
                if grep -q '"gated":true' /tmp/hf_model_response 2>/dev/null; then
                    print_warning "This is a gated model - license acceptance required"
                fi
                
                # Try to extract downloads count
                local downloads=$(grep -o '"downloads":[0-9]*' /tmp/hf_model_response 2>/dev/null | cut -d':' -f2 || echo "Unknown")
                if [[ "$downloads" != "Unknown" && -n "$downloads" ]]; then
                    print_status "Model downloads: $downloads"
                fi
            fi
            
            return 0
            ;;
        401)
            print_error "Model access failed - Authentication required"
            print_status "Your token is invalid or expired"
            return 1
            ;;
        403)
            print_error "Model access failed - Access denied"
            print_status "You don't have permission to access this model"
            print_status "This usually means:"
            print_status "  1. You haven't accepted the model's license agreement"
            print_status "  2. Your token doesn't have the required permissions"
            print_status "  3. The model requires special access approval"
            return 1
            ;;
        404)
            print_error "Model not found"
            print_status "The model $MODEL_NAME doesn't exist or has been moved"
            return 1
            ;;
        *)
            print_error "Model access test failed - HTTP $http_code"
            if [[ -f "/tmp/hf_model_response" ]]; then
                print_status "Response: $(cat /tmp/hf_model_response | head -c 200)"
            fi
            return 1
            ;;
    esac
}

# Function to test actual model download capability
test_model_download() {
    local token="$1"
    
    print_step "4" "Testing model download capability..."
    
    # Test downloading a small file (config.json)
    local http_code
    http_code=$(curl -s -w "%{http_code}" -o /tmp/hf_config_response \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "https://huggingface.co/$MODEL_NAME/resolve/main/config.json" 2>/dev/null)
    
    case $http_code in
        200)
            print_success "Model download test successful"
            
            # Basic validation that it's JSON-like
            if [[ -f "/tmp/hf_config_response" ]] && grep -q '{' /tmp/hf_config_response 2>/dev/null; then
                print_status "Successfully downloaded config.json"
                
                # Try to extract model type
                local model_type=$(grep -o '"model_type":"[^"]*"' /tmp/hf_config_response 2>/dev/null | cut -d'"' -f4 || echo "Unknown")
                if [[ "$model_type" != "Unknown" && -n "$model_type" ]]; then
                    print_status "Model type: $model_type"
                fi
            else
                print_warning "Downloaded file but content validation failed"
            fi
            
            return 0
            ;;
        401|403)
            print_error "Model download failed - Access denied"
            print_status "You cannot download model files"
            return 1
            ;;
        404)
            print_error "Model config file not found"
            return 1
            ;;
        *)
            print_error "Model download test failed - HTTP $http_code"
            return 1
            ;;
    esac
}

# Function to provide help and instructions
show_help_instructions() {
    echo ""
    print_header "📋 How to Fix HuggingFace Token Issues"
    echo ""
    
    print_status "1. Get a HuggingFace Token:"
    echo "   • Go to: https://huggingface.co/settings/tokens"
    echo "   • Click 'New token'"
    echo "   • Choose 'Read' permissions (minimum required)"
    echo "   • Copy the token (starts with hf_)"
    echo ""
    
    print_status "2. Accept MedGemma License:"
    echo "   • Go to: https://huggingface.co/$MODEL_NAME"
    echo "   • Read and accept the license agreement"
    echo "   • This is required for gated models like MedGemma"
    echo ""
    
    print_status "3. Configure Your Token:"
    echo "   • Method 1 - Environment file:"
    echo "     echo 'HUGGINGFACE_TOKEN=hf_your_token_here' >> .env.phase1"
    echo ""
    echo "   • Method 2 - Environment variable:"
    echo "     export HUGGINGFACE_TOKEN=hf_your_token_here"
    echo ""
    echo "   • Method 3 - Command line:"
    echo "     $0 hf_your_token_here"
    echo ""
    
    print_status "4. Verify Token:"
    echo "   • Run this script again: $0"
    echo "   • All tests should pass before deployment"
    echo ""
}

# Function to run all validation tests
run_validation_tests() {
    local token="$1"
    local test_count=0
    local passed_count=0
    
    print_header "🧪 Running HuggingFace Token Validation Tests"
    echo ""
    
    # Test 1: Token syntax
    ((test_count++))
    if validate_token_syntax "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 2: Basic authentication
    ((test_count++))
    if test_hf_authentication "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 3: Model access
    ((test_count++))
    if test_model_access "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 4: Model download
    ((test_count++))
    if test_model_download "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Summary
    print_header "📊 Validation Summary"
    print_success "Passed: $passed_count/$test_count tests"
    
    if [[ $passed_count -eq $test_count ]]; then
        print_success "✅ All tests passed! Your token is ready for deployment."
        echo ""
        print_status "🚀 Next steps:"
        echo "  • Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d"
        echo "  • Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f"
        echo "  • Test deployment: ./scripts/test-phase1.sh"
        return 0
    else
        print_error "❌ Some tests failed. Please fix the issues before deployment."
        return 1
    fi
}

# Main function
main() {
    show_banner
    
    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [token]"
        echo ""
        echo "Simple HuggingFace token validation (no Python required)"
        echo ""
        echo "Arguments:"
        echo "  token    Optional HuggingFace token (if not provided, will look in env files)"
        echo ""
        echo "Examples:"
        echo "  $0                           # Auto-detect token from environment"
        echo "  $0 hf_your_token_here       # Test specific token"
        echo ""
        show_help_instructions
        exit 0
    fi
    
    # Check prerequisites
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    # Get token
    local token
    if ! token=$(get_huggingface_token "$@"); then
        echo ""
        show_help_instructions
        exit 1
    fi
    
    # Run validation tests
    if run_validation_tests "$token"; then
        # Cleanup temp files
        rm -f /tmp/hf_auth_response /tmp/hf_model_response /tmp/hf_config_response
        exit 0
    else
        echo ""
        show_help_instructions
        # Cleanup temp files
        rm -f /tmp/hf_auth_response /tmp/hf_model_response /tmp/hf_config_response
        exit 1
    fi
}

# Run main function
main "$@"
