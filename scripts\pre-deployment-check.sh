#!/bin/bash

# Pre-Deployment Validation Script
# Comprehensive checks before deploying any phase

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_check() {
    echo -e "${PURPLE}[CHECK]${NC} $1"
}

# Configuration
PHASE=${1:-1}
ENV_FILE=".env.phase${PHASE}"

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                Pre-Deployment Validation                    ║"
    print_header "║                    Phase $PHASE Checks                           ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check system requirements
check_system_requirements() {
    print_check "System requirements..."
    
    local checks_passed=0
    local total_checks=0
    
    # Check Docker
    ((total_checks++))
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker found: $docker_version"
        ((checks_passed++))
    else
        print_error "Docker not found"
        print_status "Install: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
    fi
    
    # Check Docker Compose
    ((total_checks++))
    if command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker Compose found: $compose_version"
        ((checks_passed++))
    else
        print_error "Docker Compose not found"
        print_status "Install: sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose && sudo chmod +x /usr/local/bin/docker-compose"
    fi
    
    # Check available memory
    ((total_checks++))
    local available_memory=$(free -g | awk 'NR==2{print $7}')
    if [[ $available_memory -ge 12 ]]; then
        print_success "Available memory: ${available_memory}GB (sufficient)"
        ((checks_passed++))
    else
        print_warning "Available memory: ${available_memory}GB (may be insufficient)"
        print_status "Recommended: 12GB+ available memory for MedGemma model"
    fi
    
    # Check disk space
    ((total_checks++))
    local available_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $available_space -ge 20 ]]; then
        print_success "Available disk space: ${available_space}GB (sufficient)"
        ((checks_passed++))
    else
        print_warning "Available disk space: ${available_space}GB (may be insufficient)"
        print_status "Recommended: 20GB+ available space for model and data"
    fi
    
    echo ""
    print_status "System checks: $checks_passed/$total_checks passed"
    
    if [[ $checks_passed -eq $total_checks ]]; then
        return 0
    else
        return 1
    fi
}

# Function to check environment configuration
check_environment_config() {
    print_check "Environment configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error "Environment file not found: $ENV_FILE"
        print_status "Create from template: cp ${ENV_FILE}.example $ENV_FILE"
        return 1
    fi
    
    print_success "Environment file found: $ENV_FILE"
    
    # Load environment
    source "$ENV_FILE"
    
    local config_issues=0
    
    # Check API_KEY
    if [[ -z "$API_KEY" || "$API_KEY" == "your-super-secret-api-key-32-chars-minimum-change-this" ]]; then
        print_error "API_KEY not configured"
        ((config_issues++))
    else
        if [[ ${#API_KEY} -lt 32 ]]; then
            print_warning "API_KEY is shorter than recommended 32 characters"
        else
            print_success "API_KEY configured (${#API_KEY} characters)"
        fi
    fi
    
    # Check HUGGINGFACE_TOKEN
    if [[ -z "$HUGGINGFACE_TOKEN" || "$HUGGINGFACE_TOKEN" == "hf_your_token_here_replace_with_actual_token" ]]; then
        print_error "HUGGINGFACE_TOKEN not configured"
        ((config_issues++))
    else
        print_success "HUGGINGFACE_TOKEN configured"
    fi
    
    # Check REDIS_PASSWORD
    if [[ -z "$REDIS_PASSWORD" || "$REDIS_PASSWORD" == "your-secure-redis-password-change-this" ]]; then
        print_error "REDIS_PASSWORD not configured"
        ((config_issues++))
    else
        print_success "REDIS_PASSWORD configured"
    fi
    
    # Phase-specific checks
    if [[ $PHASE -ge 3 ]]; then
        # Check DOMAIN_NAME for SSL phases
        if [[ -z "$DOMAIN_NAME" || "$DOMAIN_NAME" == "your-domain.com" ]]; then
            print_error "DOMAIN_NAME not configured (required for Phase $PHASE)"
            ((config_issues++))
        else
            print_success "DOMAIN_NAME configured: $DOMAIN_NAME"
        fi
        
        # Check SSL_EMAIL
        if [[ -z "$SSL_EMAIL" || "$SSL_EMAIL" == "<EMAIL>" ]]; then
            print_error "SSL_EMAIL not configured (required for Phase $PHASE)"
            ((config_issues++))
        else
            print_success "SSL_EMAIL configured: $SSL_EMAIL"
        fi
    fi
    
    echo ""
    if [[ $config_issues -eq 0 ]]; then
        print_success "Environment configuration is complete"
        return 0
    else
        print_error "$config_issues configuration issues found"
        print_status "Edit configuration: nano $ENV_FILE"
        return 1
    fi
}

# Function to validate HuggingFace token
validate_huggingface_token() {
    print_check "HuggingFace token validation..."
    
    if [[ -f "scripts/validate-hf-token.sh" ]]; then
        print_status "Running HuggingFace token validation..."
        
        if ./scripts/validate-hf-token.sh > /tmp/hf-validation.log 2>&1; then
            print_success "HuggingFace token validation passed"
            return 0
        else
            print_error "HuggingFace token validation failed"
            print_status "Validation output:"
            cat /tmp/hf-validation.log | tail -10
            print_status "Run detailed validation: ./scripts/validate-hf-token.sh"
            return 1
        fi
    else
        print_warning "HuggingFace validation script not found"
        print_status "Manual validation recommended"
        return 0
    fi
}

# Function to check Docker Compose file
check_docker_compose() {
    print_check "Docker Compose configuration..."
    
    local compose_file="docker-compose.phase${PHASE}.yml"
    
    if [[ ! -f "$compose_file" ]]; then
        print_error "Docker Compose file not found: $compose_file"
        return 1
    fi
    
    print_success "Docker Compose file found: $compose_file"
    
    # Validate Docker Compose syntax
    if docker-compose -f "$compose_file" config > /dev/null 2>&1; then
        print_success "Docker Compose syntax is valid"
    else
        print_error "Docker Compose syntax validation failed"
        print_status "Check syntax: docker-compose -f $compose_file config"
        return 1
    fi
    
    return 0
}

# Function to check network connectivity
check_network_connectivity() {
    print_check "Network connectivity..."
    
    local connectivity_issues=0
    
    # Check internet connectivity
    if curl -s --connect-timeout 10 https://google.com > /dev/null; then
        print_success "Internet connectivity working"
    else
        print_error "Internet connectivity failed"
        ((connectivity_issues++))
    fi
    
    # Check HuggingFace connectivity
    if curl -s --connect-timeout 10 https://huggingface.co > /dev/null; then
        print_success "HuggingFace connectivity working"
    else
        print_error "HuggingFace connectivity failed"
        ((connectivity_issues++))
    fi
    
    # Check Docker Hub connectivity
    if curl -s --connect-timeout 10 https://hub.docker.com > /dev/null; then
        print_success "Docker Hub connectivity working"
    else
        print_warning "Docker Hub connectivity failed (may affect image pulls)"
    fi
    
    echo ""
    if [[ $connectivity_issues -eq 0 ]]; then
        print_success "Network connectivity is good"
        return 0
    else
        print_error "$connectivity_issues network connectivity issues"
        return 1
    fi
}

# Function to check for conflicting services
check_conflicting_services() {
    print_check "Conflicting services..."
    
    local conflicts=0
    
    # Check if any MedGemma services are already running
    for p in 1 2 3 4; do
        if [[ $p -ne $PHASE ]] && docker-compose -f "docker-compose.phase${p}.yml" ps 2>/dev/null | grep -q "Up"; then
            print_warning "Phase $p services are running (may conflict)"
            print_status "Stop with: docker-compose -f docker-compose.phase${p}.yml down"
            ((conflicts++))
        fi
    done
    
    # Check for port conflicts
    local ports_to_check=()
    case $PHASE in
        1) ports_to_check=(8000 6379) ;;
        2) ports_to_check=(80 6379) ;;
        3|4) ports_to_check=(80 443 6379) ;;
    esac
    
    for port in "${ports_to_check[@]}"; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            print_warning "Port $port is already in use"
            ((conflicts++))
        fi
    done
    
    if [[ $conflicts -eq 0 ]]; then
        print_success "No conflicting services detected"
        return 0
    else
        print_warning "$conflicts potential conflicts detected"
        return 0  # Don't fail deployment for warnings
    fi
}

# Function to run all checks
run_all_checks() {
    local failed_checks=0
    local total_checks=0
    
    echo ""
    print_header "🔍 Running Pre-Deployment Checks for Phase $PHASE"
    echo ""
    
    # System requirements
    ((total_checks++))
    if ! check_system_requirements; then
        ((failed_checks++))
    fi
    echo ""
    
    # Environment configuration
    ((total_checks++))
    if ! check_environment_config; then
        ((failed_checks++))
    fi
    echo ""
    
    # HuggingFace token validation
    ((total_checks++))
    if ! validate_huggingface_token; then
        ((failed_checks++))
    fi
    echo ""
    
    # Docker Compose
    ((total_checks++))
    if ! check_docker_compose; then
        ((failed_checks++))
    fi
    echo ""
    
    # Network connectivity
    ((total_checks++))
    if ! check_network_connectivity; then
        ((failed_checks++))
    fi
    echo ""
    
    # Conflicting services
    ((total_checks++))
    if ! check_conflicting_services; then
        # Don't count this as a failure
        :
    fi
    echo ""
    
    # Summary
    print_header "📊 Pre-Deployment Check Summary"
    local passed_checks=$((total_checks - failed_checks))
    print_status "Passed: $passed_checks/$total_checks checks"
    
    if [[ $failed_checks -eq 0 ]]; then
        print_success "✅ All checks passed! Ready for Phase $PHASE deployment."
        echo ""
        print_status "🚀 Deploy Phase $PHASE:"
        echo "  docker-compose -f docker-compose.phase${PHASE}.yml --env-file $ENV_FILE up -d"
        return 0
    else
        print_error "❌ $failed_checks checks failed. Please fix issues before deployment."
        echo ""
        print_status "🔧 Common fixes:"
        echo "  • Configure environment: nano $ENV_FILE"
        echo "  • Validate HF token: ./scripts/validate-hf-token.sh"
        echo "  • Check documentation: docs/HUGGINGFACE_TOKEN_TROUBLESHOOTING.md"
        return 1
    fi
}

# Main function
main() {
    show_banner
    
    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [phase_number]"
        echo ""
        echo "Pre-deployment validation for MedGemma AI Chat"
        echo ""
        echo "Arguments:"
        echo "  phase_number    Phase to validate (1-4, default: 1)"
        echo ""
        echo "Examples:"
        echo "  $0              # Validate Phase 1"
        echo "  $0 2            # Validate Phase 2"
        echo "  $0 3            # Validate Phase 3"
        exit 0
    fi
    
    # Validate phase number
    if [[ $PHASE -lt 1 || $PHASE -gt 4 ]]; then
        print_error "Invalid phase number: $PHASE"
        print_status "Valid phases: 1, 2, 3, 4"
        exit 1
    fi
    
    # Run all checks
    if run_all_checks; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
