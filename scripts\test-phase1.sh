#!/bin/bash

# Phase 1 Testing Script
# Comprehensive testing for Phase 1 deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test configuration
API_BASE_URL="http://localhost:8000"
TEST_TIMEOUT=30

# Load environment variables
if [[ -f ".env.phase1" ]]; then
    source .env.phase1
    print_status "Loaded .env.phase1 configuration"
else
    print_error ".env.phase1 file not found"
    exit 1
fi

# Get EC2 public IP if available
if command -v curl &> /dev/null; then
    EC2_IP=$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "")
    if [[ -n "$EC2_IP" ]]; then
        API_BASE_URL="http://$EC2_IP:8000"
        print_status "Detected EC2 public IP: $EC2_IP"
    fi
fi

print_status "Testing Phase 1 deployment at: $API_BASE_URL"

# Function to test HTTP endpoint
test_endpoint() {
    local endpoint="$1"
    local expected_status="$2"
    local description="$3"
    local method="${4:-GET}"
    local data="${5:-}"
    local headers="${6:-}"
    
    print_status "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/test_response --connect-timeout $TEST_TIMEOUT"
    
    if [[ -n "$headers" ]]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [[ "$method" == "POST" && -n "$data" ]]; then
        curl_cmd="$curl_cmd -X POST -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE_URL$endpoint'"
    
    local status_code
    status_code=$(eval $curl_cmd)
    
    if [[ "$status_code" == "$expected_status" ]]; then
        print_success "$description - Status: $status_code"
        return 0
    else
        print_error "$description - Expected: $expected_status, Got: $status_code"
        if [[ -f "/tmp/test_response" ]]; then
            print_error "Response: $(cat /tmp/test_response)"
        fi
        return 1
    fi
}

# Function to test with API key
test_api_endpoint() {
    local endpoint="$1"
    local expected_status="$2"
    local description="$3"
    local method="${4:-GET}"
    local data="${5:-}"
    
    local headers="-H 'Authorization: Bearer $API_KEY' -H 'Content-Type: application/json'"
    test_endpoint "$endpoint" "$expected_status" "$description" "$method" "$data" "$headers"
}

# Start testing
echo ""
print_status "🧪 Starting Phase 1 comprehensive testing..."
echo ""

# Test 1: Check if services are running
print_status "1. Checking Docker services..."
if docker-compose -f docker-compose.phase1.yml ps | grep -q "Up"; then
    print_success "Docker services are running"
else
    print_error "Docker services are not running properly"
    print_status "Service status:"
    docker-compose -f docker-compose.phase1.yml ps
    exit 1
fi

# Test 2: Basic connectivity
print_status "2. Testing basic connectivity..."
if test_endpoint "/" "200" "Root endpoint"; then
    print_success "Basic connectivity working"
else
    print_error "Basic connectivity failed"
    exit 1
fi

# Test 3: Health check
print_status "3. Testing health endpoint..."
if test_endpoint "/health" "200" "Health check"; then
    print_success "Health check passed"
    
    # Check health response content
    if grep -q "healthy" /tmp/test_response; then
        print_success "Health response contains 'healthy'"
    else
        print_warning "Health response doesn't contain 'healthy'"
        print_status "Health response: $(cat /tmp/test_response)"
    fi
else
    print_error "Health check failed"
    exit 1
fi

# Test 4: API authentication
print_status "4. Testing API authentication..."

# Test without API key (should fail)
if test_endpoint "/chat" "401" "Chat without API key" "POST" '{"message":"test"}' "-H 'Content-Type: application/json'"; then
    print_success "API authentication working (correctly rejected request without key)"
else
    print_warning "API authentication test inconclusive"
fi

# Test 5: Chat endpoint with API key
print_status "5. Testing chat endpoint..."
chat_data='{"message":"Hello, this is a test message for Phase 1 deployment"}'
if test_api_endpoint "/chat" "200" "Chat with API key" "POST" "$chat_data"; then
    print_success "Chat endpoint working"
    
    # Check response content
    if grep -q "response" /tmp/test_response; then
        print_success "Chat response contains 'response' field"
    else
        print_warning "Chat response format unexpected"
        print_status "Chat response: $(cat /tmp/test_response)"
    fi
else
    print_error "Chat endpoint failed"
    # Don't exit here as model might still be loading
fi

# Test 6: Image analysis endpoint
print_status "6. Testing image analysis endpoint..."

# Create a small test image (1x1 pixel PNG)
echo -n "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > /tmp/test_image.png

if curl -s -w '%{http_code}' -o /tmp/test_response --connect-timeout $TEST_TIMEOUT \
   -H "Authorization: Bearer $API_KEY" \
   -F "message=Analyze this test image" \
   -F "image=@/tmp/test_image.png" \
   "$API_BASE_URL/analyze-image" | grep -q "200"; then
    print_success "Image analysis endpoint working"
else
    print_warning "Image analysis endpoint test failed (model might still be loading)"
fi

# Test 7: Conversation endpoints
print_status "7. Testing conversation management..."

# Test conversation list
if test_api_endpoint "/conversations" "200" "List conversations"; then
    print_success "Conversation list endpoint working"
else
    print_warning "Conversation list endpoint failed"
fi

# Test 8: Metrics endpoint
print_status "8. Testing metrics endpoint..."
if test_endpoint "/metrics" "200" "Metrics endpoint"; then
    print_success "Metrics endpoint working"
else
    print_warning "Metrics endpoint failed"
fi

# Test 9: API documentation
print_status "9. Testing API documentation..."
if test_endpoint "/docs" "200" "API documentation"; then
    print_success "API documentation accessible"
else
    print_warning "API documentation not accessible (might be disabled in production)"
fi

# Test 10: Redis connectivity
print_status "10. Testing Redis connectivity..."
if docker-compose -f docker-compose.phase1.yml exec -T redis redis-cli ping | grep -q "PONG"; then
    print_success "Redis connectivity working"
else
    print_error "Redis connectivity failed"
fi

# Test 11: Model loading status
print_status "11. Checking model loading status..."
if test_endpoint "/health" "200" "Model status check"; then
    if grep -q "model_loaded.*true" /tmp/test_response; then
        print_success "Model is loaded and ready"
    else
        print_warning "Model might still be loading"
        print_status "Check logs: docker-compose -f docker-compose.phase1.yml logs medgemma-api"
    fi
fi

# Test 12: Resource usage
print_status "12. Checking resource usage..."
print_status "Docker container stats:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | grep -E "(medgemma|redis)"

# Test 13: Log files
print_status "13. Checking log files..."
if [[ -d "data/phase1/logs" ]]; then
    log_count=$(find data/phase1/logs -name "*.log" 2>/dev/null | wc -l)
    print_status "Found $log_count log files"
    
    if [[ $log_count -gt 0 ]]; then
        print_success "Log files are being created"
    else
        print_warning "No log files found yet"
    fi
else
    print_warning "Log directory not found"
fi

# Test 14: Data persistence
print_status "14. Checking data persistence..."
directories=("model_cache" "uploads" "logs" "redis")
for dir in "${directories[@]}"; do
    if [[ -d "data/phase1/$dir" ]]; then
        print_success "Data directory exists: $dir"
    else
        print_error "Data directory missing: $dir"
    fi
done

# Summary
echo ""
print_status "🎯 Phase 1 Testing Summary"
echo ""

# Check overall health
if test_endpoint "/health" "200" "Final health check"; then
    print_success "✅ Phase 1 deployment is healthy and functional!"
    echo ""
    print_status "🌐 Access your application:"
    echo "  • Web Interface: $API_BASE_URL"
    echo "  • API Documentation: $API_BASE_URL/docs"
    echo "  • Health Check: $API_BASE_URL/health"
    echo ""
    print_status "🔧 Management commands:"
    echo "  • View logs: docker-compose -f docker-compose.phase1.yml logs -f"
    echo "  • Stop services: docker-compose -f docker-compose.phase1.yml down"
    echo "  • Restart services: docker-compose -f docker-compose.phase1.yml restart"
    echo ""
    print_status "🚀 Ready to upgrade to Phase 2?"
    echo "  • Run: ./scripts/setup-phase2-directories.sh"
    echo "  • Configure: cp .env.phase1 .env.phase2 && nano .env.phase2"
    echo "  • Deploy: ./scripts/migrate-phase1-to-phase2.sh"
else
    print_error "❌ Phase 1 deployment has issues"
    echo ""
    print_status "🔍 Troubleshooting steps:"
    echo "  • Check logs: docker-compose -f docker-compose.phase1.yml logs"
    echo "  • Check services: docker-compose -f docker-compose.phase1.yml ps"
    echo "  • Restart services: docker-compose -f docker-compose.phase1.yml restart"
    echo "  • Check configuration: cat .env.phase1"
fi

# Cleanup
rm -f /tmp/test_response /tmp/test_image.png

echo ""
print_status "Phase 1 testing completed!"
