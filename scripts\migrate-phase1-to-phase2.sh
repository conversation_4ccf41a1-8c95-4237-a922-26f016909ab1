#!/bin/bash

# Migration Script: Phase 1 to Phase 2
# Safely migrates from standalone API to Nginx reverse proxy setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔄 Migrating from Phase 1 to Phase 2..."

# Check if Phase 1 is running
if docker-compose -f docker-compose.phase1.yml ps | grep -q "Up"; then
    print_status "Phase 1 services detected, preparing for migration..."
    
    # Create backup of Phase 1 data
    print_status "Creating backup of Phase 1 data..."
    BACKUP_DIR="backups/phase1-to-phase2-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup environment file
    if [[ -f ".env.phase1" ]]; then
        cp .env.phase1 "$BACKUP_DIR/"
        print_success "Backed up .env.phase1"
    fi
    
    # Backup data directories
    if [[ -d "data/phase1" ]]; then
        cp -r data/phase1 "$BACKUP_DIR/"
        print_success "Backed up Phase 1 data"
    fi
    
    print_success "Backup created at: $BACKUP_DIR"
    
    # Stop Phase 1 services gracefully
    print_status "Stopping Phase 1 services..."
    docker-compose -f docker-compose.phase1.yml down
    print_success "Phase 1 services stopped"
    
else
    print_warning "Phase 1 services not running, proceeding with setup..."
fi

# Check if .env.phase2 exists
if [[ ! -f ".env.phase2" ]]; then
    print_status "Creating .env.phase2 configuration..."
    
    if [[ -f ".env.phase1" ]]; then
        # Copy Phase 1 config as base
        cp .env.phase1 .env.phase2
        
        # Add Phase 2 specific configurations
        echo "" >> .env.phase2
        echo "# Phase 2: Nginx Configuration" >> .env.phase2
        echo "DOMAIN_NAME=localhost" >> .env.phase2
        echo "NGINX_CLIENT_MAX_BODY_SIZE=10m" >> .env.phase2
        echo "NGINX_WORKER_PROCESSES=auto" >> .env.phase2
        
        print_success "Created .env.phase2 from Phase 1 configuration"
        print_warning "Please update DOMAIN_NAME in .env.phase2 with your actual domain or IP"
        
    elif [[ -f ".env.phase2.example" ]]; then
        cp .env.phase2.example .env.phase2
        print_warning "Created .env.phase2 from template - please configure it"
    else
        print_error ".env.phase2.example not found"
        exit 1
    fi
fi

# Setup Phase 2 directories
print_status "Setting up Phase 2 directories..."
./scripts/setup-phase2-directories.sh

# Migrate data from Phase 1 to Phase 2
if [[ -d "data/phase1" ]]; then
    print_status "Migrating data from Phase 1 to Phase 2..."
    
    # Create Phase 2 data directory
    mkdir -p data/phase2
    
    # Copy model cache (avoid re-downloading)
    if [[ -d "data/phase1/model_cache" ]]; then
        print_status "Copying model cache..."
        cp -r data/phase1/model_cache data/phase2/
        print_success "Model cache migrated"
    fi
    
    # Copy conversation data
    if [[ -d "data/phase1/redis" ]]; then
        print_status "Copying conversation data..."
        cp -r data/phase1/redis data/phase2/
        print_success "Conversation data migrated"
    fi
    
    # Copy uploaded files
    if [[ -d "data/phase1/uploads" ]]; then
        print_status "Copying uploaded files..."
        cp -r data/phase1/uploads data/phase2/
        print_success "Uploaded files migrated"
    fi
    
    # Copy logs
    if [[ -d "data/phase1/logs" ]]; then
        print_status "Copying logs..."
        cp -r data/phase1/logs data/phase2/
        print_success "Logs migrated"
    fi
    
    print_success "Data migration completed"
else
    print_warning "No Phase 1 data found to migrate"
fi

# Update CORS and allowed hosts for Phase 2
print_status "Updating network configuration for Phase 2..."

# Get current IP for configuration
EC2_IP=$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")

# Update .env.phase2 with appropriate settings
if [[ -f ".env.phase2" ]]; then
    # Update DOMAIN_NAME if it's still localhost
    if grep -q "DOMAIN_NAME=localhost" .env.phase2; then
        sed -i "s/DOMAIN_NAME=localhost/DOMAIN_NAME=$EC2_IP/" .env.phase2
        print_status "Updated DOMAIN_NAME to $EC2_IP"
    fi
    
    # Update CORS_ORIGINS for Phase 2
    if grep -q "CORS_ORIGINS=\*" .env.phase2; then
        sed -i "s/CORS_ORIGINS=\*/CORS_ORIGINS=http:\/\/$EC2_IP,http:\/\/localhost/" .env.phase2
        print_status "Updated CORS_ORIGINS for Phase 2"
    fi
    
    # Update ALLOWED_HOSTS
    if grep -q "ALLOWED_HOSTS=\*" .env.phase2; then
        sed -i "s/ALLOWED_HOSTS=\*/ALLOWED_HOSTS=$EC2_IP,localhost/" .env.phase2
        print_status "Updated ALLOWED_HOSTS for Phase 2"
    fi
fi

# Start Phase 2 services
print_status "Starting Phase 2 services..."
docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Verify Phase 2 deployment
print_status "Verifying Phase 2 deployment..."

# Check if services are running
if docker-compose -f docker-compose.phase2.yml ps | grep -q "Up"; then
    print_success "Phase 2 services are running"
else
    print_error "Phase 2 services failed to start"
    print_status "Checking logs..."
    docker-compose -f docker-compose.phase2.yml logs
    exit 1
fi

# Test HTTP access through Nginx
print_status "Testing HTTP access through Nginx..."
if curl -s --connect-timeout 10 "http://$EC2_IP/api/health" | grep -q "healthy"; then
    print_success "HTTP access through Nginx working"
else
    print_warning "HTTP access test failed, checking configuration..."
    docker-compose -f docker-compose.phase2.yml logs nginx
fi

# Test web interface
if curl -s --connect-timeout 10 "http://$EC2_IP/" | grep -q "html"; then
    print_success "Web interface accessible through Nginx"
else
    print_warning "Web interface test failed"
fi

# Display migration summary
echo ""
print_success "🎉 Migration from Phase 1 to Phase 2 completed!"
echo ""
print_status "📊 Migration Summary:"
echo "  ✅ Phase 1 services stopped"
echo "  ✅ Data migrated to Phase 2"
echo "  ✅ Nginx reverse proxy configured"
echo "  ✅ Phase 2 services started"
echo ""
print_status "🌐 Access URLs (Phase 2):"
echo "  • Web Interface: http://$EC2_IP"
echo "  • API Base: http://$EC2_IP/api/"
echo "  • Health Check: http://$EC2_IP/api/health"
echo "  • API Docs: http://$EC2_IP/api/docs"
echo ""
print_status "🔧 Management Commands:"
echo "  • View logs: docker-compose -f docker-compose.phase2.yml logs -f"
echo "  • Stop services: docker-compose -f docker-compose.phase2.yml down"
echo "  • Restart services: docker-compose -f docker-compose.phase2.yml restart"
echo ""
print_status "📁 Data Locations:"
echo "  • Phase 2 data: ./data/phase2/"
echo "  • Backup: ./$BACKUP_DIR"
echo ""
print_status "🚀 Next Steps:"
echo "  • Test all functionality with Phase 2"
echo "  • Update any external integrations to use new URLs"
echo "  • Consider upgrading to Phase 3 for HTTPS security"
echo ""
print_status "🔄 Rollback (if needed):"
echo "  • Stop Phase 2: docker-compose -f docker-compose.phase2.yml down"
echo "  • Restore Phase 1: docker-compose -f docker-compose.phase1.yml up -d"
echo "  • Restore data from backup if needed"

# Run Phase 2 tests
print_status "Running Phase 2 verification tests..."
if [[ -f "scripts/test-phase2.sh" ]]; then
    ./scripts/test-phase2.sh
else
    print_warning "Phase 2 test script not found, skipping automated tests"
fi

print_success "Phase 1 to Phase 2 migration completed successfully!"
