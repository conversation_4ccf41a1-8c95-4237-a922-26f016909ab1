# Phase 2: Production Nginx Setup Configuration
# Extends Phase 1 with Nginx reverse proxy (HTTP only)
# Copy this file to .env.phase2 and customize the values

# =============================================================================
# API CONFIGURATION (from Phase 1)
# =============================================================================

API_KEY=your-super-secret-api-key-32-chars-minimum-change-this
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=production

# =============================================================================
# MODEL CONFIGURATION (from Phase 1)
# =============================================================================

MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9
WORKERS=1

# =============================================================================
# HUGGINGFACE AUTHENTICATION (from Phase 1)
# =============================================================================

HUGGINGFACE_TOKEN=hf_your_token_here_replace_with_actual_token
HF_TOKEN=hf_your_token_here_replace_with_actual_token

# =============================================================================
# NETWORK CONFIGURATION (Updated for Phase 2)
# =============================================================================

# Domain name or IP address (REQUIRED for Phase 2)
# Use your EC2 public IP or domain name
DOMAIN_NAME=your-ec2-ip-or-domain.com

# CORS origins (updated for domain)
CORS_ORIGINS=http://localhost,http://your-ec2-ip-or-domain.com
ALLOWED_HOSTS=localhost,your-ec2-ip-or-domain.com

# =============================================================================
# NGINX CONFIGURATION (New for Phase 2)
# =============================================================================

# Nginx settings
NGINX_CLIENT_MAX_BODY_SIZE=10m
NGINX_WORKER_PROCESSES=auto

# =============================================================================
# FILE UPLOAD CONFIGURATION (from Phase 1)
# =============================================================================

UPLOAD_MAX_SIZE=10485760

# =============================================================================
# CONVERSATION MANAGEMENT (from Phase 1)
# =============================================================================

CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# =============================================================================
# REDIS CONFIGURATION (from Phase 1)
# =============================================================================

REDIS_PASSWORD=your-secure-redis-password-change-this

# =============================================================================
# PHASE 2 SPECIFIC SETTINGS
# =============================================================================

# Phase 2 adds Nginx reverse proxy on port 80
# API is accessible through Nginx at http://your-domain/api/
# Frontend is accessible at http://your-domain/

# =============================================================================
# MIGRATION FROM PHASE 1
# =============================================================================

# To migrate from Phase 1 to Phase 2:
# 1. Stop Phase 1: docker-compose -f docker-compose.phase1.yml down
# 2. Copy data: ./scripts/migrate-phase1-to-phase2.sh
# 3. Update this config file
# 4. Deploy Phase 2: docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d

# =============================================================================
# DEPLOYMENT COMMANDS FOR PHASE 2
# =============================================================================

# After configuring this file:
# 1. Copy to .env.phase2: cp .env.phase2.example .env.phase2
# 2. Edit values: nano .env.phase2
# 3. Create data directories: ./scripts/setup-phase2-directories.sh
# 4. Deploy: docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d
# 5. Monitor: docker-compose -f docker-compose.phase2.yml logs -f
# 6. Test: curl http://your-domain/api/health

# =============================================================================
# PHASE 2 BENEFITS
# =============================================================================

# ✅ Professional reverse proxy setup
# ✅ Static file serving optimization
# ✅ Request/response header management
# ✅ Basic security headers
# ✅ Centralized logging
# ✅ Load balancing preparation
# ✅ All Phase 1 functionality preserved

# =============================================================================
# NEXT STEPS
# =============================================================================

# Ready for Phase 3? Add SSL/TLS security:
# - Configure domain name properly
# - Set up DNS records
# - Migrate to Phase 3 for HTTPS support
