# Simplified MedGemma EC2 Deployment

This guide provides a simplified deployment approach using only local environment files for configuration.

## 🚀 Quick Start

### Prerequisites
- AWS EC2 instance (t3.xlarge or larger)
- HuggingFace account with MedGemma access
- SSH access to EC2 instance

### 1-Minute Deploy
```bash
# 1. SSH into your EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# 2. Run setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/main/scripts/setup-ec2-instance.sh | bash

# 3. Log out and back in for Docker group changes
exit
ssh -i your-key.pem ubuntu@your-ec2-ip

# 4. Clone repository
cd ~/medgemma-deployment
git clone https://github.com/your-repo/docker-medgemma-fastapi.git .

# 5. Configure environment
cp .env.example .env
nano .env  # Add your HuggingFace token

# 6. Deploy
./scripts/deploy-ec2.sh
```

## 🔧 Configuration

### Environment File Setup

1. **Copy template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit configuration**:
   ```bash
   nano .env
   ```

3. **Required settings**:
   ```bash
   # REQUIRED: Your HuggingFace token
   HUGGINGFACE_TOKEN=hf_your_actual_token_here
   HF_TOKEN=hf_your_actual_token_here
   
   # REQUIRED: Secure API key
   API_KEY=your-secure-api-key
   
   # REQUIRED: Redis password
   REDIS_PASSWORD=your-secure-redis-password
   ```

4. **Secure the file**:
   ```bash
   chmod 600 .env
   ```

### Getting HuggingFace Token

1. Visit [HuggingFace Settings](https://huggingface.co/settings/tokens)
2. Create new token with "Read" permissions
3. Accept [MedGemma license](https://huggingface.co/google/medgemma-4b-it)
4. Copy token (starts with `hf_`)

## 🔒 Security Features

### File Security
- `.env` file permissions set to 600 (owner read/write only)
- `.env` automatically added to `.gitignore`
- No sensitive data in version control

### Token Validation
- Automatic token format validation
- Connection testing during deployment
- Clear error messages for authentication issues

### Network Security
- Configurable CORS origins
- Security group recommendations
- HTTPS support for production

## 📊 Monitoring

### Built-in Monitoring
- **Health checks**: `/health` endpoint
- **Metrics**: Prometheus metrics on port 9090
- **Logs**: Structured JSON logging
- **Grafana**: Dashboard on port 3000

### Access URLs
After deployment, access these services:
- **API**: http://your-ec2-ip:8000
- **API Docs**: http://your-ec2-ip:8000/docs
- **Grafana**: http://your-ec2-ip:3000 (admin/admin)
- **Prometheus**: http://your-ec2-ip:9091

## 🛠 Management Commands

### Check Status
```bash
# View running services
docker-compose -f docker-compose.ec2.yml ps

# Check logs
docker-compose -f docker-compose.ec2.yml logs -f medgemma-api

# Monitor resources
docker stats
```

### Update Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose -f docker-compose.ec2.yml build --no-cache
docker-compose -f docker-compose.ec2.yml up -d
```

### Backup Configuration
```bash
# Backup .env file securely
cp .env .env.backup.$(date +%Y%m%d)
chmod 600 .env.backup.*

# Backup model cache
sudo tar -czf model-cache-backup-$(date +%Y%m%d).tar.gz /opt/medgemma/model_cache
```

## 🔍 Troubleshooting

### Common Issues

#### Token Authentication Failed
```bash
# Check token in .env file
grep HUGGINGFACE_TOKEN .env

# Verify token format (should start with hf_)
# Test token manually
curl -H "Authorization: Bearer hf_your_token" https://huggingface.co/api/whoami
```

#### Model Download Issues
```bash
# Check available disk space
df -h

# Check memory usage
free -h

# View download logs
docker-compose -f docker-compose.ec2.yml logs medgemma-api | grep -i download
```

#### Container Won't Start
```bash
# Check environment variables
docker-compose -f docker-compose.ec2.yml exec medgemma-api env | grep HF

# Validate .env file
if [ -f .env ]; then echo "✓ .env exists"; else echo "✗ .env missing"; fi
```

### Validation Script
```bash
# Run comprehensive validation
./scripts/validate-ec2-deployment.sh
```

## 📋 Best Practices

### Security
1. **Never commit .env files** to version control
2. **Use strong passwords** for all services
3. **Rotate tokens regularly** for security
4. **Restrict security groups** to necessary IPs
5. **Monitor access logs** for suspicious activity

### Performance
1. **Use appropriate instance types** (t3.xlarge minimum)
2. **Monitor resource usage** regularly
3. **Clean up Docker images** periodically
4. **Use persistent volumes** for model cache

### Maintenance
1. **Regular backups** of configuration and data
2. **Keep system updated** with security patches
3. **Monitor logs** for errors and warnings
4. **Test deployments** in staging environment first

## 📚 Additional Resources

- [Complete EC2 Deployment Guide](./EC2_DEPLOYMENT.md)
- [HuggingFace Authentication Guide](./HUGGINGFACE_AUTHENTICATION.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)
- [MedGemma Model Documentation](https://huggingface.co/google/medgemma-4b-it)

## 🆘 Support

If you encounter issues:

1. **Check logs**: `docker-compose -f docker-compose.ec2.yml logs`
2. **Run validation**: `./scripts/validate-ec2-deployment.sh`
3. **Review documentation**: Check the guides in the `docs/` directory
4. **Verify configuration**: Ensure `.env` file has correct values

For persistent issues, review the troubleshooting section or check the project's issue tracker.
