#!/bin/bash
set -e

# EC2-Optimized Startup Script for MedGemma FastAPI
# This script handles model download and application startup on EC2 instances

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with timestamp and colors
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to check system resources
check_system_resources() {
    log "Checking system resources..."
    
    # Check available memory
    local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
    local mem_total_gb=$((mem_total / 1024 / 1024))
    local mem_available_gb=$((mem_available / 1024 / 1024))
    
    log "Total memory: ${mem_total_gb}GB"
    log "Available memory: ${mem_available_gb}GB"
    
    # Check if we have enough memory for MedGemma (recommend 8GB+)
    if [ $mem_available_gb -lt 6 ]; then
        warn "Low memory detected (${mem_available_gb}GB). MedGemma 4B requires at least 6-8GB."
        warn "Consider using a larger EC2 instance type (t3.xlarge or larger)."
    fi
    
    # Check disk space
    local disk_available=$(df /app | tail -1 | awk '{print $4}')
    local disk_available_gb=$((disk_available / 1024 / 1024))
    
    log "Available disk space: ${disk_available_gb}GB"
    
    if [ $disk_available_gb -lt 20 ]; then
        warn "Low disk space detected (${disk_available_gb}GB). Model download requires ~15-20GB."
    fi
    
    # Check internet connectivity
    if curl -s --connect-timeout 5 https://huggingface.co > /dev/null; then
        log "Internet connectivity: OK"
    else
        error "No internet connectivity. Cannot download model."
        exit 1
    fi
}

# Function to check if model is cached
check_model_cache() {
    log "Checking model cache..."
    
    if [ -d "/app/model_cache" ] && [ "$(ls -A /app/model_cache 2>/dev/null)" ]; then
        # Check for key model files
        if find /app/model_cache -name "config.json" -o -name "*.safetensors" -o -name "pytorch_model*.bin" | grep -q .; then
            success "Model files found in cache"
            return 0
        fi
    fi
    
    log "Model not found in cache"
    return 1
}

# Function to validate HuggingFace token
validate_hf_token() {
    local token="$1"
    
    if [ -z "$token" ]; then
        return 1
    fi
    
    # Check token format (should start with hf_)
    if [[ ! "$token" =~ ^hf_ ]]; then
        warn "Token doesn't start with 'hf_' - this may not be a valid HuggingFace token"
    fi
    
    # Test token by trying to access the model info
    log "Validating HuggingFace token..."
    
    if python3 -c "
import os
import tempfile
from huggingface_hub import HfApi
try:
    # Create temporary cache directory to avoid permission issues
    temp_cache = tempfile.mkdtemp()
    os.environ['HF_HOME'] = temp_cache
    os.environ['HF_HUB_CACHE'] = temp_cache
    os.environ['TRANSFORMERS_CACHE'] = temp_cache

    # Test authentication without login (which writes to disk)
    api = HfApi(token='$token')
    user_info = api.whoami()
    print(f'Token validated for user: {user_info.get(\"name\", \"Unknown\")}')

    # Test model access
    model_info = api.model_info('google/medgemma-4b-it')
    print(f'Model access confirmed: {model_info.id}')

    # Test file access
    files = list(api.list_repo_files('google/medgemma-4b-it', repo_type='model'))
    print(f'Model files accessible: {len(files)} files found')

    # Cleanup temp directory
    import shutil
    shutil.rmtree(temp_cache, ignore_errors=True)

    print('Token validation successful')
    exit(0)
except Exception as e:
    print(f'Token validation failed: {e}')
    exit(1)
" 2>/dev/null; then
        success "HuggingFace token is valid"
        return 0
    else
        error "HuggingFace token validation failed"
        return 1
    fi
}

# Function to get HuggingFace token from environment variables
get_hf_token() {
    local token=""

    # Try environment variables
    if [ -n "$HUGGINGFACE_TOKEN" ]; then
        token="$HUGGINGFACE_TOKEN"
        log "Using HUGGINGFACE_TOKEN environment variable"
    elif [ -n "$HF_TOKEN" ]; then
        token="$HF_TOKEN"
        log "Using HF_TOKEN environment variable"
    fi

    if [ -z "$token" ]; then
        error "No HuggingFace token found!"
        error "Please set HUGGINGFACE_TOKEN environment variable."
        error ""
        error "To set the token:"
        error "1. Add to .env file: HUGGINGFACE_TOKEN=hf_your_token_here"
        error "2. Or export directly: export HUGGINGFACE_TOKEN=hf_your_token_here"
        error ""
        error "Get your token from: https://huggingface.co/settings/tokens"
        error "Accept MedGemma license: https://huggingface.co/google/medgemma-4b-it"
        return 1
    fi

    # Validate the token
    if validate_hf_token "$token"; then
        echo "$token"
        return 0
    else
        error "Invalid HuggingFace token"
        return 1
    fi
}

# Function to download model with progress monitoring
download_model() {
    local token="$1"
    
    log "Starting MedGemma model download..."
    log "This may take 10-30 minutes depending on your internet connection"
    
    # Set environment variables for the download
    export HUGGINGFACE_TOKEN="$token"
    export HF_TOKEN="$token"
    export HF_HOME="/app/model_cache"
    export TRANSFORMERS_CACHE="/app/model_cache"
    
    # Run the download script with timeout and retry logic
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log "Download attempt $attempt/$max_attempts"
        
        if timeout 3600 python3 scripts/download_model_runtime.py; then
            success "Model download completed successfully"
            return 0
        else
            error "Download attempt $attempt failed"
            if [ $attempt -lt $max_attempts ]; then
                local wait_time=$((attempt * 30))
                warn "Retrying in $wait_time seconds..."
                sleep $wait_time
            fi
        fi
        
        attempt=$((attempt + 1))
    done
    
    error "All download attempts failed"
    return 1
}

# Function to start the FastAPI application
start_application() {
    log "Starting MedGemma FastAPI application..."
    
    # Set runtime environment variables
    export HF_HOME="/app/model_cache"
    export TRANSFORMERS_CACHE="/app/model_cache"
    
    # Start the application with proper error handling
    exec python3 -m uvicorn app.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 1 \
        --timeout-keep-alive 300 \
        --access-log \
        --log-level info
}

# Function to handle graceful shutdown
cleanup() {
    log "Received shutdown signal, cleaning up..."
    # Kill any background processes
    jobs -p | xargs -r kill
    exit 0
}

# Main startup function
main() {
    echo "=== MedGemma FastAPI EC2 Startup ==="
    echo "Optimized for AWS EC2 deployment"
    echo "Container started at: $(date)"
    echo
    
    # Set up signal handlers for graceful shutdown
    trap cleanup SIGTERM SIGINT
    
    # Check system resources
    check_system_resources
    
    # Check if model is already cached
    if check_model_cache; then
        log "Model is already cached, skipping download"
    else
        log "Model not cached, starting download process..."
        
        # Get HuggingFace token
        local token
        if ! token=$(get_hf_token); then
            error "Cannot proceed without valid HuggingFace token"
            exit 1
        fi
        
        # Download the model
        if ! download_model "$token"; then
            error "Model download failed"
            exit 1
        fi
    fi
    
    # Start the application
    success "Model ready, starting FastAPI application..."
    start_application
}

# Run main function
main "$@"
