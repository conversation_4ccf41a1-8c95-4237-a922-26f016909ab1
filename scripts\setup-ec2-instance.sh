#!/bin/bash
# EC2 Instance Setup Script for MedGemma
# This script prepares an EC2 instance for MedGemma deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        error "Cannot detect OS"
        exit 1
    fi
    
    log "Detected OS: $OS $VER"
}

# Function to update system packages
update_system() {
    log "Updating system packages..."
    
    case $OS in
        "Ubuntu"*)
            sudo apt-get update
            sudo apt-get upgrade -y
            sudo apt-get install -y curl wget git unzip htop
            ;;
        "Amazon Linux"*)
            sudo yum update -y
            sudo yum install -y curl wget git unzip htop
            ;;
        *)
            warn "Unsupported OS: $OS"
            ;;
    esac
    
    success "System packages updated"
}

# Function to install Docker
install_docker() {
    log "Installing Docker..."
    
    case $OS in
        "Ubuntu"*)
            # Remove old versions
            sudo apt-get remove -y docker docker-engine docker.io containerd runc || true
            
            # Install dependencies
            sudo apt-get install -y \
                apt-transport-https \
                ca-certificates \
                curl \
                gnupg \
                lsb-release
            
            # Add Docker's official GPG key
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            
            # Set up repository
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # Install Docker Engine
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
            
        "Amazon Linux"*)
            sudo yum install -y docker
            ;;
    esac
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    success "Docker installed successfully"
}

# Function to install Docker Compose
install_docker_compose() {
    log "Installing Docker Compose..."
    
    # Install Docker Compose v2
    local compose_version="2.24.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/v${compose_version}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # Create symlink for compatibility
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    success "Docker Compose installed successfully"
}

# Function to setup environment file template
setup_env_template() {
    log "Creating environment file template..."

    # Create .env.example file
    cat > ~/medgemma-deployment/.env.example << EOF
# MedGemma Configuration Template
# Copy this file to .env and fill in your values

# HuggingFace Configuration (REQUIRED)
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here

# API Configuration
API_KEY=your-secure-api-key-here
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# Model Configuration
MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# Performance Configuration
WORKERS=1
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# Redis Configuration
REDIS_PASSWORD=your-redis-password-here

# Domain Configuration (optional)
DOMAIN_NAME=your-domain.com

# Monitoring Configuration
ENABLE_METRICS=true
GRAFANA_PASSWORD=admin
EOF

    # Create .gitignore to protect .env
    cat > ~/medgemma-deployment/.gitignore << EOF
# Environment files (contain sensitive data)
.env
.env.local
.env.production

# Model cache (large files)
model_cache/

# Logs
logs/
*.log

# Docker volumes
uploads/

# Temporary files
*.tmp
*.temp

# OS generated files
.DS_Store
Thumbs.db
EOF

    success "Environment template and .gitignore created"
}

# Function to create directory structure
create_directories() {
    log "Creating directory structure..."
    
    # Create application directories
    sudo mkdir -p /opt/medgemma/{model_cache,uploads,logs,redis}
    sudo chown -R $USER:$USER /opt/medgemma
    sudo chmod -R 755 /opt/medgemma
    
    # Create project directory
    mkdir -p ~/medgemma-deployment
    cd ~/medgemma-deployment
    
    success "Directory structure created"
}

# Function to configure system limits
configure_system_limits() {
    log "Configuring system limits..."
    
    # Increase file descriptor limits
    echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # Configure Docker daemon
    sudo mkdir -p /etc/docker
    cat << EOF | sudo tee /etc/docker/daemon.json
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "5"
    },
    "storage-driver": "overlay2"
}
EOF
    
    success "System limits configured"
}

# Function to setup firewall
setup_firewall() {
    log "Setting up firewall..."
    
    case $OS in
        "Ubuntu"*)
            # Install and configure UFW
            sudo apt-get install -y ufw
            
            # Default policies
            sudo ufw default deny incoming
            sudo ufw default allow outgoing
            
            # Allow SSH
            sudo ufw allow ssh
            
            # Allow HTTP/HTTPS
            sudo ufw allow 80/tcp
            sudo ufw allow 443/tcp
            
            # Allow application ports
            sudo ufw allow 8000/tcp  # FastAPI
            sudo ufw allow 3000/tcp  # Grafana
            sudo ufw allow 9091/tcp  # Prometheus
            
            # Enable firewall
            sudo ufw --force enable
            ;;
            
        "Amazon Linux"*)
            # Configure iptables for Amazon Linux
            sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
            sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
            sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
            sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
            sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
            sudo iptables -A INPUT -p tcp --dport 9091 -j ACCEPT
            sudo service iptables save
            ;;
    esac
    
    success "Firewall configured"
}

# Function to optimize system for ML workloads
optimize_system() {
    log "Optimizing system for ML workloads..."
    
    # Increase shared memory size
    echo "tmpfs /dev/shm tmpfs defaults,size=8g 0 0" | sudo tee -a /etc/fstab
    
    # Configure swap (if not already configured)
    if [ ! -f /swapfile ]; then
        log "Creating swap file..."
        sudo fallocate -l 4G /swapfile
        sudo chmod 600 /swapfile
        sudo mkswap /swapfile
        sudo swapon /swapfile
        echo "/swapfile none swap sw 0 0" | sudo tee -a /etc/fstab
    fi
    
    # Set swappiness
    echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf
    
    success "System optimized for ML workloads"
}

# Function to create monitoring setup
setup_monitoring() {
    log "Setting up monitoring configuration..."
    
    # Create monitoring directory
    mkdir -p ~/medgemma-deployment/monitoring
    
    # Create Prometheus configuration
    cat << EOF > ~/medgemma-deployment/monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'medgemma-api'
    static_configs:
      - targets: ['medgemma-api:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
EOF
    
    success "Monitoring configuration created"
}

# Function to create deployment script
create_deployment_script() {
    log "Creating deployment script..."

    cat << 'EOF' > ~/medgemma-deployment/deploy.sh
#!/bin/bash
# Quick deployment script for MedGemma

set -e

echo "=== MedGemma Quick Deploy ==="

# Check if .env exists
if [ ! -f .env ]; then
    echo "Creating .env file from template..."

    if [ -f .env.example ]; then
        cp .env.example .env
        echo "Copied .env.example to .env"
        echo "Please edit .env file and add your HuggingFace token and other configuration."
        echo "Required: HUGGINGFACE_TOKEN=hf_your_token_here"
        echo ""
        read -p "Press Enter to continue after editing .env file..."
    else
        echo "No .env.example found. Creating basic .env file..."
        read -p "Enter HuggingFace token (hf_...): " HF_TOKEN
        read -p "Enter API key (or press enter for auto-generated): " API_KEY

        if [ -z "$API_KEY" ]; then
            API_KEY="medgemma-$(date +%s)"
        fi

        cat > .env << ENVEOF
# MedGemma Configuration
HUGGINGFACE_TOKEN=$HF_TOKEN
HF_TOKEN=$HF_TOKEN
API_KEY=$API_KEY
REDIS_PASSWORD=redis-$(openssl rand -hex 16)
GRAFANA_PASSWORD=admin
ENVIRONMENT=production
LOG_LEVEL=INFO
MODEL_NAME=google/medgemma-4b-it
WORKERS=1
CORS_ORIGINS=*
ENVEOF

        # Secure the file
        chmod 600 .env
        echo "Environment file created and secured"
    fi
fi

# Verify .env file has required variables
if ! grep -q "HUGGINGFACE_TOKEN=" .env || grep -q "HUGGINGFACE_TOKEN=hf_your_token_here" .env; then
    echo "ERROR: Please set a valid HUGGINGFACE_TOKEN in .env file"
    echo "Edit .env and set: HUGGINGFACE_TOKEN=hf_your_actual_token"
    exit 1
fi

# Secure .env file
chmod 600 .env

# Deploy with docker-compose
echo "Starting deployment..."
docker-compose -f docker-compose.ec2.yml up -d

echo "Deployment started. Check status with:"
echo "docker-compose -f docker-compose.ec2.yml ps"
echo "docker-compose -f docker-compose.ec2.yml logs -f"
EOF

    chmod +x ~/medgemma-deployment/deploy.sh

    success "Deployment script created"
}

# Function to display final instructions
display_instructions() {
    local public_ip=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "your-ec2-ip")
    
    echo
    success "EC2 instance setup completed!"
    echo
    echo "=== Next Steps ==="
    echo "1. Log out and log back in to apply Docker group changes:"
    echo "   exit"
    echo "   ssh back into your instance"
    echo
    echo "2. Clone your MedGemma repository:"
    echo "   cd ~/medgemma-deployment"
    echo "   git clone <your-repo-url> ."
    echo
    echo "3. Configure your environment:"
    echo "   cp .env.example .env"
    echo "   nano .env  # Edit and add your HuggingFace token"
    echo
    echo "4. Deploy the application:"
    echo "   ./deploy.sh"
    echo
    echo "=== Access URLs (after deployment) ==="
    echo "Application: http://$public_ip:8000"
    echo "API Docs: http://$public_ip:8000/docs"
    echo "Grafana: http://$public_ip:3000 (admin/admin)"
    echo "Prometheus: http://$public_ip:9091"
    echo
    echo "=== Important Notes ==="
    echo "- Make sure your EC2 security group allows inbound traffic on ports 80, 443, 8000, 3000, 9091"
    echo "- Get your HuggingFace token from: https://huggingface.co/settings/tokens"
    echo "- Accept MedGemma license: https://huggingface.co/google/medgemma-4b-it"
    echo "- Keep your .env file secure (permissions 600) and never commit it to version control"
    echo "- Monitor logs: docker-compose -f docker-compose.ec2.yml logs -f"
    echo
}

# Main function
main() {
    echo "=== EC2 Instance Setup for MedGemma ==="
    echo "This script will prepare your EC2 instance for MedGemma deployment"
    echo
    
    # Detect OS
    detect_os
    
    # Update system
    update_system
    
    # Install Docker
    install_docker
    
    # Install Docker Compose
    install_docker_compose

    # Create directories
    create_directories

    # Setup environment template
    setup_env_template
    
    # Configure system
    configure_system_limits
    
    # Setup firewall
    setup_firewall
    
    # Optimize system
    optimize_system
    
    # Setup monitoring
    setup_monitoring
    
    # Create deployment script
    create_deployment_script
    
    # Display instructions
    display_instructions
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0"
    echo
    echo "This script sets up an EC2 instance for MedGemma deployment."
    echo "It will install Docker, Docker Compose, AWS CLI, and configure the system."
    echo
    echo "Supported OS:"
    echo "- Ubuntu 20.04+"
    echo "- Amazon Linux 2"
    echo
    echo "Recommended EC2 instance types:"
    echo "- t3.xlarge (4 vCPU, 16GB RAM) - Minimum"
    echo "- t3.2xlarge (8 vCPU, 32GB RAM) - Recommended"
    echo "- m5.xlarge (4 vCPU, 16GB RAM) - Good performance"
    echo "- c5.xlarge (4 vCPU, 8GB RAM) - CPU optimized"
    exit 0
fi

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    error "Please do not run this script as root"
    exit 1
fi

# Run main function
main "$@"
