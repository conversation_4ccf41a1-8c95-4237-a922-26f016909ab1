# HuggingFace CLI Token Validation Guide

## 🎯 Overview

This guide shows you how to use the official HuggingFace CLI for token validation instead of direct API calls. The CLI method is more reliable and follows HuggingFace's recommended practices.

## 🚀 Quick Start

### Step 1: Install HuggingFace CLI
```bash
# Automated installation
./scripts/install-hf-cli.sh

# Or manual installation
pip install huggingface_hub[cli] --upgrade
```

### Step 2: Validate Your Token
```bash
# Use the CLI-based validation (recommended)
./scripts/validate-hf-token-cli.sh

# Or use the automatic script (tries CLI first, falls back to API)
./scripts/validate-hf-token.sh
```

### Step 3: Fix Any Issues
Follow the specific guidance provided by the validation script.

## 📋 Installation Methods

### Method 1: Automated Script (Recommended)
```bash
# Run the installation script
./scripts/install-hf-cli.sh

# This will:
# - Check for existing installation
# - Verify Python and pip
# - Install huggingface_hub[cli]
# - Setup PATH if needed
# - Verify installation works
```

### Method 2: Manual Installation
```bash
# Using pip3
pip3 install huggingface_hub[cli] --upgrade --user

# Using python3 -m pip
python3 -m pip install huggingface_hub[cli] --upgrade --user

# Verify installation
huggingface-cli --version
```

### Method 3: System Package Manager
```bash
# Ubuntu/Debian (if available)
sudo apt update
sudo apt install python3-huggingface-hub

# Or use pip with system packages
sudo pip3 install huggingface_hub[cli]
```

## 🔧 CLI Validation Features

### What the CLI Validation Tests

1. **Prerequisites Check**
   - Verifies `huggingface-cli` is installed
   - Checks Python availability
   - Shows version information

2. **Token Syntax Validation**
   - Validates token format (hf_ + 34 characters)
   - Checks token length and characters

3. **CLI Authentication Test**
   - Uses `huggingface-cli whoami` command
   - Shows authenticated user information
   - Displays organization memberships

4. **Model Access Test**
   - Tests access to `google/medgemma-4b-it`
   - Uses CLI commands for model information
   - Detects gated model status

5. **Download Capability Test**
   - Downloads `config.json` using CLI
   - Tests actual file download capability
   - Validates downloaded content

### CLI Commands Used

```bash
# Authentication test
huggingface-cli whoami

# Model access test
huggingface-cli repo info google/medgemma-4b-it

# Download test
huggingface-cli download google/medgemma-4b-it config.json --local-dir /tmp/test
```

## 🎯 Usage Examples

### Basic Validation
```bash
# Auto-detect token from environment
./scripts/validate-hf-token-cli.sh

# Test specific token
./scripts/validate-hf-token-cli.sh hf_your_token_here
```

### Interactive Login
```bash
# Login interactively (stores token)
huggingface-cli login

# Then validate
./scripts/validate-hf-token-cli.sh
```

### Manual Testing
```bash
# Test authentication manually
huggingface-cli whoami

# Test model access manually
huggingface-cli download google/medgemma-4b-it config.json --dry-run

# Test actual download
huggingface-cli download google/medgemma-4b-it config.json --local-dir ./test
```

## 📊 Expected Output

### Successful Validation
```
🧪 Running HuggingFace CLI Token Validation

[STEP 0] Checking prerequisites...
[SUCCESS] HuggingFace CLI found: huggingface_hub, version 0.20.3
[SUCCESS] Python found: Python 3.9.7
[SUCCESS] Prerequisites check passed

[STEP 1] Validating token syntax...
[SUCCESS] Token syntax is valid

[STEP 2] Testing HuggingFace CLI authentication...
[SUCCESS] Authentication successful
[INFO] Authenticated as: your-username
[INFO] Email: <EMAIL>

[STEP 3] Testing access to google/medgemma-4b-it...
[SUCCESS] Model access successful
[WARNING] This is a gated model - license acceptance required

[STEP 4] Testing model download capability...
[INFO] Testing download of config.json to temporary directory...
[SUCCESS] Model download test successful
[INFO] Successfully downloaded config.json
[INFO] Model type: gemma
[INFO] Vocab size: 256000

📊 Validation Summary
[SUCCESS] Passed: 5/5 tests
✅ All tests passed! Your token is ready for deployment.
```

## 🚨 Troubleshooting

### CLI Not Found
```bash
# Check if CLI is installed
which huggingface-cli

# Check Python packages
pip3 list | grep huggingface

# Install if missing
./scripts/install-hf-cli.sh
```

### PATH Issues
```bash
# Check if CLI is in user directory
ls ~/.local/bin/huggingface-cli

# Add to PATH temporarily
export PATH="$HOME/.local/bin:$PATH"

# Add to PATH permanently
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Permission Issues
```bash
# Install to user directory
pip3 install huggingface_hub[cli] --user

# Or use virtual environment
python3 -m venv hf-env
source hf-env/bin/activate
pip install huggingface_hub[cli]
```

### Token Issues
```bash
# Check current token
huggingface-cli whoami

# Login interactively
huggingface-cli login

# Logout and re-login
huggingface-cli logout
huggingface-cli login
```

## 🔄 Migration from API Validation

### Differences from API Method

| Feature | API Method | CLI Method |
|---------|------------|------------|
| **Dependencies** | curl, bash | Python, huggingface_hub |
| **Authentication** | Manual HTTP headers | Built-in token management |
| **Error Handling** | HTTP status codes | CLI exit codes and messages |
| **Model Access** | Direct API calls | Official CLI commands |
| **Download Test** | curl download | CLI download command |
| **Reliability** | Network dependent | More robust error handling |

### When to Use Each Method

**Use CLI Method When:**
- Python and pip are available
- You want the most reliable validation
- You plan to use HuggingFace CLI for other tasks
- You need detailed error messages

**Use API Method When:**
- Python is not available
- You need a lightweight solution
- CLI installation is not possible
- You prefer bash-only solutions

## 🎉 Integration with Deployment

### Pre-Deployment Validation
```bash
# Run comprehensive pre-deployment check
./scripts/pre-deployment-check.sh

# This automatically uses CLI validation if available
```

### Automated Deployment
```bash
# Validate before deployment
if ./scripts/validate-hf-token-cli.sh; then
    echo "✅ Token validated, proceeding with deployment"
    docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d
else
    echo "❌ Token validation failed, aborting deployment"
    exit 1
fi
```

### CI/CD Integration
```yaml
# Example GitHub Actions step
- name: Validate HuggingFace Token
  run: |
    ./scripts/install-hf-cli.sh
    ./scripts/validate-hf-token-cli.sh
  env:
    HUGGINGFACE_TOKEN: ${{ secrets.HUGGINGFACE_TOKEN }}
```

## 📚 Additional Resources

### HuggingFace CLI Documentation
- [Official CLI Guide](https://huggingface.co/docs/huggingface_hub/guides/cli)
- [Authentication Guide](https://huggingface.co/docs/huggingface_hub/quick-start#authentication)
- [Download Guide](https://huggingface.co/docs/huggingface_hub/guides/download)

### Token Management
- [Create Tokens](https://huggingface.co/settings/tokens)
- [Token Permissions](https://huggingface.co/docs/hub/security-tokens)
- [Gated Models](https://huggingface.co/docs/hub/models-gated)

### Model Access
- [MedGemma Model](https://huggingface.co/google/medgemma-4b-it)
- [License Agreement](https://huggingface.co/google/medgemma-4b-it/blob/main/LICENSE)
- [Model Documentation](https://huggingface.co/google/medgemma-4b-it/blob/main/README.md)

## 🆘 Getting Help

### If CLI Validation Fails
1. **Check CLI installation**: `huggingface-cli --version`
2. **Test manual commands**: `huggingface-cli whoami`
3. **Check token format**: Ensure it starts with `hf_` and is 37 characters
4. **Accept license**: Visit the MedGemma model page and accept the license
5. **Wait for propagation**: License acceptance can take 5-10 minutes

### Support Channels
- **HuggingFace Discord**: https://discord.gg/huggingface
- **HuggingFace Forums**: https://discuss.huggingface.co
- **GitHub Issues**: https://github.com/huggingface/huggingface_hub/issues

### Debug Information
```bash
# Collect debug information
echo "=== HuggingFace CLI Debug Report ===" > cli-debug.log
echo "Date: $(date)" >> cli-debug.log
echo "CLI Version: $(huggingface-cli --version 2>&1)" >> cli-debug.log
echo "Python Version: $(python3 --version 2>&1)" >> cli-debug.log
echo "Token Length: ${#HUGGINGFACE_TOKEN}" >> cli-debug.log
echo "" >> cli-debug.log
./scripts/validate-hf-token-cli.sh >> cli-debug.log 2>&1
```

The CLI-based validation provides more reliable and detailed feedback about your HuggingFace token configuration, making it easier to identify and fix authentication issues before deployment.
