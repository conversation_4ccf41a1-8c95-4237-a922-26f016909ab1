# Phase 2: Production Nginx Setup (HTTP)
# Extends Phase 1 with professional reverse proxy setup
# Features: All Phase 1 + Nginx proxy, static file serving, basic security headers

version: '3.8'

services:
  nginx:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx
    container_name: medgemma-nginx-phase2
    restart: unless-stopped
    
    ports:
      # HTTP access through Nginx
      - "80:80"
      
    volumes:
      # Nginx configuration
      - ./nginx/nginx.phase2.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.phase2.conf:/etc/nginx/conf.d/default.conf:ro
      # Static frontend files
      - ./frontend:/usr/share/nginx/html:ro
      # Nginx logs
      - nginx_logs_phase2:/var/log/nginx
      
    environment:
      - API_UPSTREAM=medgemma-api:8000
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - NGINX_CLIENT_MAX_BODY_SIZE=${NGINX_CLIENT_MAX_BODY_SIZE:-10m}
      - NGINX_WORKER_PROCESSES=${NGINX_WORKER_PROCESSES:-auto}
      
    depends_on:
      medgemma-api:
        condition: service_healthy
        
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    networks:
      - medgemma-network-phase2

  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
      args:
        HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
        HF_TOKEN: ${HUGGINGFACE_TOKEN}
    container_name: medgemma-api-phase2
    restart: unless-stopped
    
    environment:
      # Model Configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      - WORKERS=${WORKERS:-1}
      
      # API Configuration
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # Network Configuration (internal only)
      - HOST=0.0.0.0
      - PORT=8000
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost,http://${DOMAIN_NAME:-localhost}}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,${DOMAIN_NAME:-localhost}}
      
      # File Upload Configuration
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      
      # Conversation Management
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - CONVERSATION_TTL=${CONVERSATION_TTL:-86400}
      
      # HuggingFace Authentication
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
    volumes:
      # Reuse Phase 1 data or create new Phase 2 data
      - model_cache_phase2:/app/model_cache
      - upload_data_phase2:/app/uploads
      - logs_phase2:/app/logs
      
    # No external port exposure - access through Nginx only
    expose:
      - "8000"
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
      
    depends_on:
      redis:
        condition: service_healthy
        
    deploy:
      resources:
        limits:
          memory: 14G
        reservations:
          memory: 12G
          
    networks:
      - medgemma-network-phase2

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-phase2
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      - redis_data_phase2:/data
      
    expose:
      - "6379"
      
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
          
    networks:
      - medgemma-network-phase2

volumes:
  # Phase 2 specific volumes
  model_cache_phase2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase2/model_cache
      
  upload_data_phase2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase2/uploads
      
  logs_phase2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase2/logs
      
  nginx_logs_phase2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase2/nginx_logs
      
  redis_data_phase2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase2/redis

networks:
  medgemma-network-phase2:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
