#!/bin/bash
set -e

echo "=== MedGemma FastAPI Startup ==="
echo "Starting container initialization..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if model is cached
check_model_cache() {
    if [ -d "/app/model_cache" ] && [ "$(ls -A /app/model_cache 2>/dev/null)" ]; then
        log "Model cache directory exists and is not empty"
        # Check for key model files
        if find /app/model_cache -name "config.json" -o -name "pytorch_model.bin" -o -name "*.safetensors" | grep -q .; then
            log "Model files found in cache"
            return 0
        fi
    fi
    log "Model not found in cache"
    return 1
}

# Function to download model
download_model() {
    log "Starting model download..."
    
    # Check if HuggingFace token is available
    if [ -z "$HUGGINGFACE_TOKEN" ] && [ -z "$HF_TOKEN" ]; then
        log "ERROR: No HuggingFace token found!"
        log "Please set HUGGINGFACE_TOKEN environment variable."
        log "Get your token from: https://huggingface.co/settings/tokens"
        log "Make sure you've accepted the MedGemma license at: https://huggingface.co/google/medgemma-4b-it"
        exit 1
    fi
    
    # Run the runtime download script
    if python scripts/download_model_runtime.py; then
        log "Model download completed successfully"
        return 0
    else
        log "ERROR: Model download failed"
        exit 1
    fi
}

# Function to start the application
start_app() {
    log "Starting FastAPI application..."
    exec python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
}

# Main startup logic
main() {
    log "Checking model cache..."
    
    if check_model_cache; then
        log "Model is already cached, skipping download"
    else
        log "Model not cached, downloading..."
        download_model
    fi
    
    log "Model ready, starting application..."
    start_app
}

# Handle signals for graceful shutdown
trap 'log "Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Run main function
main
