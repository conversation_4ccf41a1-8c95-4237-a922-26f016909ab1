version: '3.8'

# EC2-Optimized Docker Compose for MedGemma FastAPI
# Designed for AWS EC2 deployment with proper resource management

services:
  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
    container_name: medgemma-api-ec2
    restart: unless-stopped
    environment:
      # Model Configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MODEL_CACHE_DIR=/app/model_cache
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      
      # API Configuration
      - API_KEY=${API_KEY}
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # HuggingFace Authentication
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_HOME=/app/model_cache
      - TRANSFORMERS_CACHE=/app/model_cache
      
      # Performance Configuration
      - WORKERS=${WORKERS:-1}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-300}
      
      # Security Configuration
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-*}
      
      # File Upload Configuration
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      - UPLOAD_DIR=/app/uploads
      - ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff
      
      # Database Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - CONVERSATION_TTL=86400
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      - LOG_DIR=/app/logs
      
      # Monitoring Configuration
      - ENABLE_METRICS=${ENABLE_METRICS:-true}
      - METRICS_PORT=9090
      
      # Health Check Configuration
      - HEALTH_CHECK_INTERVAL=30
      - HEALTH_CHECK_TIMEOUT=15
    
    volumes:
      # Persistent model cache (crucial for EC2 to avoid re-downloading)
      - model_cache:/app/model_cache
      - upload_data:/app/uploads
      - logs:/app/logs
    
    ports:
      - "8000:8000"
      - "9090:9090"  # Metrics port
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 300s  # Extended for model download
    
    depends_on:
      redis:
        condition: service_healthy
    
    deploy:
      resources:
        limits:
          # Adjust based on EC2 instance size
          memory: 12G
          cpus: '3.0'
        reservations:
          memory: 8G
          cpus: '2.0'
    
    networks:
      - medgemma-network
    
    # Logging configuration for EC2
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  nginx:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx
    container_name: medgemma-nginx-ec2
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./frontend:/usr/share/nginx/html:ro
      - ssl_certs:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - API_UPSTREAM=medgemma-api:8000
    depends_on:
      medgemma-api:
        condition: service_healthy
    networks:
      - medgemma-network
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-ec2
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - medgemma-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Monitoring stack for EC2
  prometheus:
    image: prom/prometheus:latest
    container_name: medgemma-prometheus-ec2
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - medgemma-network
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  grafana:
    image: grafana/grafana:latest
    container_name: medgemma-grafana-ec2
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - medgemma-network
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

volumes:
  # Persistent volumes for EC2 deployment
  model_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/medgemma/model_cache
  
  upload_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/medgemma/uploads
  
  logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/medgemma/logs
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/medgemma/redis
  
  ssl_certs:
    driver: local
  
  nginx_logs:
    driver: local
  
  prometheus_data:
    driver: local
  
  grafana_data:
    driver: local

networks:
  medgemma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
