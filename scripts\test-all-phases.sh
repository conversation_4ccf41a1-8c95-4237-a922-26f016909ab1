#!/bin/bash

# Comprehensive Testing Script for All Phases
# Tests functionality across Phase 1, 2, 3, and 4 deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_phase() {
    echo -e "${PURPLE}[PHASE $1]${NC} $2"
}

# Test configuration
TEST_TIMEOUT=30
PHASE=""
API_BASE_URL=""
PROTOCOL="http"

# Function to detect active phase
detect_active_phase() {
    print_status "🔍 Detecting active deployment phase..."
    
    # Check for running containers
    if docker-compose -f docker-compose.phase4.yml ps 2>/dev/null | grep -q "Up"; then
        PHASE="4"
        print_phase "4" "Phase 4 (Enterprise) deployment detected"
    elif docker-compose -f docker-compose.phase3.yml ps 2>/dev/null | grep -q "Up"; then
        PHASE="3"
        print_phase "3" "Phase 3 (HTTPS) deployment detected"
    elif docker-compose -f docker-compose.phase2.yml ps 2>/dev/null | grep -q "Up"; then
        PHASE="2"
        print_phase "2" "Phase 2 (Nginx) deployment detected"
    elif docker-compose -f docker-compose.phase1.yml ps 2>/dev/null | grep -q "Up"; then
        PHASE="1"
        print_phase "1" "Phase 1 (Standalone) deployment detected"
    else
        print_error "No active deployment detected"
        print_status "Available phases to test:"
        echo "  • Phase 1: ./scripts/test-phase1.sh"
        echo "  • Phase 2: ./scripts/test-phase2.sh"
        echo "  • Phase 3: ./scripts/test-phase3.sh"
        echo "  • Phase 4: ./scripts/test-phase4.sh"
        exit 1
    fi
}

# Function to load environment for detected phase
load_phase_environment() {
    local env_file=".env.phase${PHASE}"
    
    if [[ -f "$env_file" ]]; then
        source "$env_file"
        print_status "Loaded environment: $env_file"
    else
        print_error "Environment file not found: $env_file"
        exit 1
    fi
}

# Function to determine API base URL
determine_api_url() {
    local ec2_ip
    ec2_ip=$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")
    
    case $PHASE in
        "1")
            API_BASE_URL="http://${ec2_ip}:8000"
            PROTOCOL="http"
            ;;
        "2")
            API_BASE_URL="http://${DOMAIN_NAME:-$ec2_ip}"
            PROTOCOL="http"
            ;;
        "3"|"4")
            API_BASE_URL="https://${DOMAIN_NAME:-$ec2_ip}"
            PROTOCOL="https"
            ;;
    esac
    
    print_status "Testing endpoint: $API_BASE_URL"
}

# Function to test HTTP endpoint
test_endpoint() {
    local endpoint="$1"
    local expected_status="$2"
    local description="$3"
    local method="${4:-GET}"
    local data="${5:-}"
    local headers="${6:-}"
    
    print_status "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/test_response --connect-timeout $TEST_TIMEOUT"
    
    # Add SSL options for HTTPS
    if [[ "$PROTOCOL" == "https" ]]; then
        curl_cmd="$curl_cmd -k"
    fi
    
    if [[ -n "$headers" ]]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [[ "$method" == "POST" && -n "$data" ]]; then
        curl_cmd="$curl_cmd -X POST -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE_URL$endpoint'"
    
    local status_code
    status_code=$(eval $curl_cmd 2>/dev/null || echo "000")
    
    if [[ "$status_code" == "$expected_status" ]]; then
        print_success "$description - Status: $status_code"
        return 0
    else
        print_error "$description - Expected: $expected_status, Got: $status_code"
        if [[ -f "/tmp/test_response" ]]; then
            print_error "Response: $(cat /tmp/test_response | head -c 200)"
        fi
        return 1
    fi
}

# Function to test with API key
test_api_endpoint() {
    local endpoint="$1"
    local expected_status="$2"
    local description="$3"
    local method="${4:-GET}"
    local data="${5:-}"
    
    local headers="-H 'Authorization: Bearer $API_KEY' -H 'Content-Type: application/json'"
    test_endpoint "$endpoint" "$expected_status" "$description" "$method" "$data" "$headers"
}

# Function to run phase-specific tests
run_phase_tests() {
    print_phase "$PHASE" "Running Phase $PHASE specific tests..."
    
    case $PHASE in
        "1")
            run_phase1_tests
            ;;
        "2")
            run_phase2_tests
            ;;
        "3")
            run_phase3_tests
            ;;
        "4")
            run_phase4_tests
            ;;
    esac
}

# Phase 1 specific tests
run_phase1_tests() {
    print_status "Phase 1 specific tests:"
    
    # Test direct API access
    test_endpoint "/" "200" "Direct API root access"
    
    # Test port 8000 accessibility
    if curl -s --connect-timeout 5 "http://localhost:8000/health" | grep -q "healthy"; then
        print_success "Direct port 8000 access working"
    else
        print_warning "Direct port 8000 access failed"
    fi
}

# Phase 2 specific tests
run_phase2_tests() {
    print_status "Phase 2 specific tests:"
    
    # Test Nginx reverse proxy
    test_endpoint "/" "200" "Nginx reverse proxy"
    
    # Test API through proxy
    test_endpoint "/api/health" "200" "API through Nginx proxy"
    
    # Test static file serving
    test_endpoint "/index.html" "200" "Static file serving"
    
    # Verify no direct API access on port 8000
    if curl -s --connect-timeout 5 "http://localhost:8000/health" &>/dev/null; then
        print_warning "Direct API access still available (should be internal only)"
    else
        print_success "Direct API access properly restricted"
    fi
}

# Phase 3 specific tests
run_phase3_tests() {
    print_status "Phase 3 specific tests:"
    
    # Test HTTPS access
    test_endpoint "/" "200" "HTTPS access"
    
    # Test HTTP to HTTPS redirect
    if curl -s -I "http://${DOMAIN_NAME:-localhost}" | grep -q "301"; then
        print_success "HTTP to HTTPS redirect working"
    else
        print_warning "HTTP to HTTPS redirect not working"
    fi
    
    # Test SSL certificate
    if openssl s_client -connect "${DOMAIN_NAME:-localhost}:443" -servername "${DOMAIN_NAME:-localhost}" </dev/null 2>/dev/null | grep -q "Verify return code: 0"; then
        print_success "SSL certificate valid"
    else
        print_warning "SSL certificate validation failed (might be self-signed or staging)"
    fi
    
    # Test security headers
    if curl -s -I "$API_BASE_URL/" | grep -q "Strict-Transport-Security"; then
        print_success "HSTS header present"
    else
        print_warning "HSTS header missing"
    fi
}

# Phase 4 specific tests
run_phase4_tests() {
    print_status "Phase 4 specific tests:"
    
    # Run Phase 3 tests first
    run_phase3_tests
    
    # Test Prometheus
    if curl -s --connect-timeout 10 "http://localhost:9090/api/v1/query?query=up" | grep -q "success"; then
        print_success "Prometheus monitoring working"
    else
        print_warning "Prometheus monitoring not accessible"
    fi
    
    # Test Grafana
    if curl -s --connect-timeout 10 "http://localhost:3000/api/health" | grep -q "ok"; then
        print_success "Grafana dashboard accessible"
    else
        print_warning "Grafana dashboard not accessible"
    fi
    
    # Test backup service
    if docker-compose -f docker-compose.phase4.yml ps | grep -q "backup.*Up"; then
        print_success "Backup service running"
    else
        print_warning "Backup service not running"
    fi
    
    # Test advanced metrics
    test_endpoint "/metrics" "200" "Advanced metrics endpoint"
}

# Function to run common tests across all phases
run_common_tests() {
    print_status "🧪 Running common functionality tests..."
    
    # Test 1: Health check
    test_endpoint "/health" "200" "Health check endpoint" || test_endpoint "/api/health" "200" "Health check endpoint (via API path)"
    
    # Test 2: API authentication
    test_endpoint "/chat" "401" "Authentication required" "POST" '{"message":"test"}' "-H 'Content-Type: application/json'"
    
    # Test 3: Chat functionality
    local chat_data='{"message":"Hello, this is a comprehensive test message"}'
    test_api_endpoint "/chat" "200" "Chat endpoint" "POST" "$chat_data" || test_api_endpoint "/api/chat" "200" "Chat endpoint (via API path)" "POST" "$chat_data"
    
    # Test 4: Image analysis
    echo -n "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > /tmp/test_image.png
    
    local image_endpoint="/analyze-image"
    if [[ "$PHASE" != "1" ]]; then
        image_endpoint="/api/analyze-image"
    fi
    
    if curl -s -w '%{http_code}' -o /tmp/test_response --connect-timeout $TEST_TIMEOUT \
       -H "Authorization: Bearer $API_KEY" \
       -F "message=Test image analysis" \
       -F "image=@/tmp/test_image.png" \
       "$API_BASE_URL$image_endpoint" | grep -q "200"; then
        print_success "Image analysis endpoint working"
    else
        print_warning "Image analysis endpoint failed (model might be loading)"
    fi
    
    # Test 5: Conversation management
    local conv_endpoint="/conversations"
    if [[ "$PHASE" != "1" ]]; then
        conv_endpoint="/api/conversations"
    fi
    
    test_api_endpoint "$conv_endpoint" "200" "Conversation list endpoint"
    
    # Test 6: Service health
    print_status "Checking service health..."
    docker-compose -f "docker-compose.phase${PHASE}.yml" ps
}

# Function to generate test report
generate_test_report() {
    echo ""
    print_success "📊 Test Report Summary"
    echo ""
    print_phase "$PHASE" "Phase $PHASE deployment tested"
    print_status "Endpoint: $API_BASE_URL"
    print_status "Protocol: $PROTOCOL"
    
    if [[ -f "/tmp/test_response" ]]; then
        local last_response_size=$(wc -c < /tmp/test_response)
        print_status "Last response size: $last_response_size bytes"
    fi
    
    echo ""
    print_status "🔧 Management commands for Phase $PHASE:"
    echo "  • View logs: docker-compose -f docker-compose.phase${PHASE}.yml logs -f"
    echo "  • Restart: docker-compose -f docker-compose.phase${PHASE}.yml restart"
    echo "  • Stop: docker-compose -f docker-compose.phase${PHASE}.yml down"
    
    case $PHASE in
        "1")
            echo ""
            print_status "🚀 Upgrade options:"
            echo "  • Phase 2: ./scripts/migrate-phase1-to-phase2.sh"
            ;;
        "2")
            echo ""
            print_status "🚀 Upgrade options:"
            echo "  • Phase 3: ./scripts/migrate-phase2-to-phase3.sh"
            ;;
        "3")
            echo ""
            print_status "🚀 Upgrade options:"
            echo "  • Phase 4: ./scripts/migrate-phase3-to-phase4.sh"
            ;;
        "4")
            echo ""
            print_status "🎉 You're running the most advanced configuration!"
            echo "  • Prometheus: http://localhost:9090"
            echo "  • Grafana: http://localhost:3000"
            ;;
    esac
}

# Main execution
main() {
    echo ""
    print_status "🚀 Starting comprehensive deployment testing..."
    echo ""
    
    # Detect and configure for active phase
    detect_active_phase
    load_phase_environment
    determine_api_url
    
    # Run tests
    run_common_tests
    run_phase_tests
    
    # Generate report
    generate_test_report
    
    # Cleanup
    rm -f /tmp/test_response /tmp/test_image.png
    
    echo ""
    print_success "✅ Comprehensive testing completed!"
}

# Handle command line arguments
if [[ $# -gt 0 ]]; then
    case $1 in
        "1"|"2"|"3"|"4")
            PHASE="$1"
            print_status "Testing specific phase: $PHASE"
            ;;
        "--help"|"-h")
            echo "Usage: $0 [phase_number]"
            echo "  phase_number: 1, 2, 3, or 4 (optional, auto-detected if not specified)"
            echo ""
            echo "Examples:"
            echo "  $0        # Auto-detect and test active phase"
            echo "  $0 2      # Test Phase 2 specifically"
            exit 0
            ;;
        *)
            print_error "Invalid phase: $1"
            print_status "Valid phases: 1, 2, 3, 4"
            exit 1
            ;;
    esac
fi

# Run main function
main
