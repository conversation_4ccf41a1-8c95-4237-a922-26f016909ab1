#!/bin/bash

# Comprehensive Script Validation Tool
# Checks all bash scripts for syntax errors and common issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
TOTAL_SCRIPTS=0
VALID_SCRIPTS=0
INVALID_SCRIPTS=0
WARNING_SCRIPTS=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check bash syntax
check_bash_syntax() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    print_status "Checking syntax: $script_name"
    
    # Check if file exists and is readable
    if [[ ! -f "$script_file" ]]; then
        print_error "File not found: $script_file"
        return 1
    fi
    
    if [[ ! -r "$script_file" ]]; then
        print_error "File not readable: $script_file"
        return 1
    fi
    
    # Check bash syntax
    if bash -n "$script_file" 2>/dev/null; then
        print_success "✅ $script_name: Syntax OK"
        return 0
    else
        print_error "❌ $script_name: Syntax ERROR"
        echo "Detailed error:"
        bash -n "$script_file" 2>&1 | sed 's/^/  /'
        return 1
    fi
}

# Function to check for common issues
check_common_issues() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    local issues_found=0
    
    # Check for undefined color variables
    if grep -n "Colors\." "$script_file" >/dev/null 2>&1; then
        print_warning "⚠️  $script_name: Found 'Colors.' pattern (should be just variable name)"
        grep -n "Colors\." "$script_file" | sed 's/^/  Line /'
        issues_found=$((issues_found + 1))
    fi
    
    # Check for unmatched quotes
    local quote_count=$(grep -o '"' "$script_file" | wc -l)
    if [[ $((quote_count % 2)) -ne 0 ]]; then
        print_warning "⚠️  $script_name: Unmatched double quotes detected"
        issues_found=$((issues_found + 1))
    fi
    
    local single_quote_count=$(grep -o "'" "$script_file" | wc -l)
    if [[ $((single_quote_count % 2)) -ne 0 ]]; then
        print_warning "⚠️  $script_name: Unmatched single quotes detected"
        issues_found=$((issues_found + 1))
    fi
    
    # Check for undefined variables in color functions
    if grep -n '\${[A-Z_]*}' "$script_file" | grep -v '\${NC}\|\${RED}\|\${GREEN}\|\${YELLOW}\|\${BLUE}\|\${CYAN}' >/dev/null 2>&1; then
        print_warning "⚠️  $script_name: Potentially undefined color variables"
        grep -n '\${[A-Z_]*}' "$script_file" | grep -v '\${NC}\|\${RED}\|\${GREEN}\|\${YELLOW}\|\${BLUE}\|\${CYAN}' | sed 's/^/  Line /'
        issues_found=$((issues_found + 1))
    fi
    
    # Check for missing shebang
    if ! head -1 "$script_file" | grep -q '^#!/'; then
        print_warning "⚠️  $script_name: Missing shebang line"
        issues_found=$((issues_found + 1))
    fi
    
    return $issues_found
}

# Function to validate a single script
validate_script() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    echo ""
    print_header "═══ Validating: $script_name ═══"
    
    TOTAL_SCRIPTS=$((TOTAL_SCRIPTS + 1))
    
    # Check syntax first
    if check_bash_syntax "$script_file"; then
        # Check for common issues
        if check_common_issues "$script_file"; then
            WARNING_SCRIPTS=$((WARNING_SCRIPTS + 1))
            print_warning "Script has warnings but syntax is valid"
        else
            VALID_SCRIPTS=$((VALID_SCRIPTS + 1))
            print_success "Script is fully valid"
        fi
    else
        INVALID_SCRIPTS=$((INVALID_SCRIPTS + 1))
        print_error "Script has syntax errors"
    fi
}

# Function to find and validate all bash scripts
validate_all_scripts() {
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                Script Validation Report                     ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    
    # Find all bash scripts in the scripts directory
    local script_files=()
    
    # Add .sh files
    while IFS= read -r -d '' file; do
        script_files+=("$file")
    done < <(find scripts -name "*.sh" -type f -print0 2>/dev/null)
    
    # Add files with bash shebang
    while IFS= read -r -d '' file; do
        if head -1 "$file" 2>/dev/null | grep -q '^#!/.*bash'; then
            script_files+=("$file")
        fi
    done < <(find scripts -type f ! -name "*.sh" ! -name "*.py" ! -name "*.md" -print0 2>/dev/null)
    
    if [[ ${#script_files[@]} -eq 0 ]]; then
        print_error "No bash scripts found in scripts directory"
        return 1
    fi
    
    print_status "Found ${#script_files[@]} bash scripts to validate"
    
    # Validate each script
    for script_file in "${script_files[@]}"; do
        validate_script "$script_file"
    done
    
    # Print summary
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                    Validation Summary                       ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    print_status "Total scripts checked: $TOTAL_SCRIPTS"
    print_success "Valid scripts: $VALID_SCRIPTS"
    print_warning "Scripts with warnings: $WARNING_SCRIPTS"
    print_error "Invalid scripts: $INVALID_SCRIPTS"
    echo ""
    
    if [[ $INVALID_SCRIPTS -eq 0 ]]; then
        if [[ $WARNING_SCRIPTS -eq 0 ]]; then
            print_success "🎉 All scripts are valid!"
        else
            print_warning "⚠️  All scripts have valid syntax, but some have warnings"
        fi
        return 0
    else
        print_error "💥 Some scripts have syntax errors that need to be fixed"
        return 1
    fi
}

# Main function
main() {
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [script_file]"
        echo ""
        echo "Validates bash scripts for syntax errors and common issues"
        echo ""
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo ""
        echo "Arguments:"
        echo "  script_file   Optional: validate specific script file"
        echo "                If not provided, validates all scripts in scripts/ directory"
        echo ""
        echo "Examples:"
        echo "  $0                                    # Validate all scripts"
        echo "  $0 scripts/deploy.sh                 # Validate specific script"
        echo ""
        exit 0
    fi
    
    if [[ $# -eq 1 ]]; then
        # Validate specific script
        validate_script "$1"
    else
        # Validate all scripts
        validate_all_scripts
    fi
}

# Run main function
main "$@"
