#!/usr/bin/env python3
"""
Simple test main.py for MedGemma API testing
Uses mock responses instead of actual AI model
"""

import os
import asyncio
from fastapi import FastAP<PERSON>, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional

# Set environment for mock
os.environ["USE_MOCK_MODEL"] = "true"
os.environ["ENVIRONMENT"] = "test"

app = FastAPI(title="MedGemma Test API")
security = HTTPBearer()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Simple API key verification for testing"""
    if credentials.credentials != "dev-api-key-for-testing-only":
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

@app.get("/health")
async def health():
    return {"status": "healthy", "model_loaded": True, "version": "test"}

@app.get("/metrics")
async def metrics():
    return {"test_mode": True, "status": "running", "model": "medgemma-mock", "prometheus_enabled": False}

@app.post("/chat")
async def chat(request: dict, api_key: str = Depends(verify_api_key)):
    await asyncio.sleep(0.1)  # Simulate processing
    return {
        "response": f"Mock response to: {request.get('message', 'test')}",
        "conversation_id": request.get("conversation_id", "test-conv"),
        "model": "mock-test"
    }

@app.post("/analyze-image")
async def analyze_image(
    image: UploadFile = File(...),
    message: str = Form(...),
    api_key: str = Depends(verify_api_key)
):
    await asyncio.sleep(0.2)  # Simulate image processing
    return {
        "response": "Mock image analysis completed",
        "conversation_id": "test-conv",
        "model": "mock-test"
    }

@app.get("/")
async def root():
    return {"message": "MedGemma Test API", "status": "running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
