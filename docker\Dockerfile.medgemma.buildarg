# Multi-stage build for MedGemma FastAPI service (Build Args Approach)
FROM python:3.11-slim as base

# Build argument for HuggingFace token (will not persist in final image)
ARG HUGGINGFACE_TOKEN
ARG HF_TOKEN

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for CPU optimization
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu \
    accelerate \
    bitsandbytes \
    optimum

# Copy application code
COPY app/ ./app/
COPY scripts/download_model.py ./scripts/
COPY scripts/download_model_runtime.py ./scripts/

# Create necessary directories
RUN mkdir -p /app/model_cache /app/uploads /app/logs && \
    chown -R medgemma:medgemma /app

# Switch to non-root user for model download
USER medgemma

# Download model weights using build argument
# The token will be available during build but not in the final image
RUN if [ -n "$HUGGINGFACE_TOKEN" ]; then \
        export HUGGINGFACE_TOKEN="$HUGGINGFACE_TOKEN" && \
        export HF_TOKEN="$HUGGINGFACE_TOKEN" && \
        python scripts/download_model.py; \
    else \
        echo "Warning: No HuggingFace token provided as build argument."; \
        echo "Model will be downloaded at runtime."; \
        echo "To provide token during build, use: docker build --build-arg HUGGINGFACE_TOKEN=your_token ."; \
        touch /app/.download_at_runtime; \
    fi

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
