# HuggingFace Token Troubleshooting Guide

## 🎯 Quick Fix

**Before deploying, always validate your token:**

```bash
# Validate your HuggingFace token
./scripts/validate-hf-token.sh

# Or use the Python version for detailed info
python3 scripts/validate-hf-token.py
```

## 🔍 Common Error Messages

### Error: "Cannot proceed without valid HuggingFace token"

**Cause:** No token found or token is invalid format

**Solution:**
```bash
# Check if token is set
echo $HUGGINGFACE_TOKEN

# Check .env.phase1 file
grep HUGGINGFACE_TOKEN .env.phase1

# Validate token format
./scripts/validate-hf-token.sh
```

### Error: "Authentication failed - Invalid token"

**Cause:** Token is malformed or expired

**Solutions:**
1. **Check token format:**
   - Must start with `hf_`
   - Must be exactly 37 characters
   - Example: `hf_abcdefghijklmnopqrstuvwxyz1234567890`

2. **Generate new token:**
   - Go to: https://huggingface.co/settings/tokens
   - Click "New token"
   - <PERSON><PERSON> "Read" permissions
   - Copy the new token

### Error: "Model access failed - Access denied"

**Cause:** Haven't accepted MedGemma license agreement

**Solution:**
1. Go to: https://huggingface.co/google/medgemma-4b-it
2. Read the license agreement
3. Click "Accept" to agree to terms
4. Wait a few minutes for permissions to propagate
5. Re-run validation: `./scripts/validate-hf-token.sh`

### Error: "Model files access denied"

**Cause:** License accepted but file access still restricted

**Solutions:**
1. **Wait for propagation:** License acceptance can take 5-10 minutes
2. **Check token permissions:** Ensure token has "Read" access
3. **Re-validate:** Run `./scripts/validate-hf-token.sh` again

## 📋 Step-by-Step Token Setup

### Step 1: Create HuggingFace Account
1. Go to: https://huggingface.co/join
2. Create account with email verification
3. Complete profile setup

### Step 2: Generate Access Token
1. Go to: https://huggingface.co/settings/tokens
2. Click "New token"
3. **Name:** `MedGemma-Deployment`
4. **Type:** `Read` (minimum required)
5. Click "Generate a token"
6. **Copy the token immediately** (starts with `hf_`)

### Step 3: Accept MedGemma License
1. Go to: https://huggingface.co/google/medgemma-4b-it
2. Read the license agreement carefully
3. Click "Accept" button
4. **Wait 5-10 minutes** for permissions to propagate

### Step 4: Configure Token
Choose one method:

**Method A: Environment File (Recommended)**
```bash
echo 'HUGGINGFACE_TOKEN=hf_your_actual_token_here' >> .env.phase1
```

**Method B: Environment Variable**
```bash
export HUGGINGFACE_TOKEN=hf_your_actual_token_here
```

**Method C: Direct in Docker Compose**
```bash
# Edit docker-compose.phase1.yml
environment:
  - HUGGINGFACE_TOKEN=hf_your_actual_token_here
```

### Step 5: Validate Setup
```bash
# Run validation script
./scripts/validate-hf-token.sh

# Should show all tests passing:
# ✅ Token syntax valid
# ✅ Authentication successful  
# ✅ Model access granted
# ✅ Model files accessible
# ✅ Download capability confirmed
```

## 🔧 Validation Scripts Usage

### Basic Validation (Bash)
```bash
# Auto-detect token from environment
./scripts/validate-hf-token.sh

# Test specific token
./scripts/validate-hf-token.sh hf_your_token_here

# Show help
./scripts/validate-hf-token.sh --help
```

### Advanced Validation (Python)
```bash
# Auto-detect token with detailed info
python3 scripts/validate-hf-token.py

# Test specific token
python3 scripts/validate-hf-token.py hf_your_token_here

# Test different model
python3 scripts/validate-hf-token.py --model google/medgemma-7b-it
```

## 🚨 Troubleshooting Specific Issues

### Issue: "Token format invalid"

**Check:**
```bash
# Your token should look like this:
hf_abcdefghijklmnopqrstuvwxyz1234567890
# ^^                                   ^^
# Must start with hf_                  37 chars total
```

**Fix:**
- Regenerate token from HuggingFace settings
- Copy entire token including `hf_` prefix
- Ensure no extra spaces or characters

### Issue: "License not accepted"

**Symptoms:**
- Can see model page
- Cannot access model files
- Download fails with 403 error

**Fix:**
1. Visit: https://huggingface.co/google/medgemma-4b-it
2. Scroll down to license section
3. Click "Accept" button
4. Wait 10 minutes
5. Re-run validation

### Issue: "Network timeout"

**Symptoms:**
- Validation scripts timeout
- Cannot reach HuggingFace API

**Fix:**
```bash
# Test internet connectivity
curl -I https://huggingface.co

# Check DNS resolution
nslookup huggingface.co

# Test with longer timeout
curl --connect-timeout 60 https://huggingface.co/api/whoami
```

### Issue: "Model not found"

**Symptoms:**
- 404 error when accessing model
- Model URL seems correct

**Check:**
1. **Model name spelling:** `google/medgemma-4b-it` (exact case)
2. **Model availability:** Visit https://huggingface.co/google/medgemma-4b-it
3. **Account access:** Some models require special approval

## 🔐 Security Best Practices

### Token Security
- **Never commit tokens to git**
- **Use environment files** (.env.phase1)
- **Rotate tokens regularly**
- **Use minimal permissions** (Read-only for deployment)

### Environment File Security
```bash
# Secure .env.phase1 file
chmod 600 .env.phase1

# Add to .gitignore
echo '.env.phase*' >> .gitignore
```

### Token Validation in CI/CD
```bash
# Add to deployment scripts
if ! ./scripts/validate-hf-token.sh; then
    echo "❌ HuggingFace token validation failed"
    exit 1
fi
```

## 📊 Validation Test Details

### Test 1: Token Syntax
- Checks `hf_` prefix
- Validates 37 character length
- Ensures alphanumeric characters only

### Test 2: Authentication
- Tests `/api/whoami` endpoint
- Verifies token is recognized
- Returns user information

### Test 3: Model Access
- Tests `/api/models/google/medgemma-4b-it` endpoint
- Checks license acceptance
- Returns model metadata

### Test 4: File Access
- Tests `/api/models/google/medgemma-4b-it/tree/main` endpoint
- Verifies file listing permissions
- Confirms download capability

### Test 5: Download Test
- Downloads `config.json` file
- Validates JSON parsing
- Confirms actual file access

## 🆘 Getting Help

### If Validation Still Fails

1. **Check HuggingFace Status:**
   - Visit: https://status.huggingface.co
   - Check for service outages

2. **Contact Support:**
   - HuggingFace: https://huggingface.co/support
   - Include validation script output

3. **Community Help:**
   - HuggingFace Discord: https://discord.gg/huggingface
   - Stack Overflow: Tag `huggingface`

### Debug Information to Collect

```bash
# Run validation with verbose output
./scripts/validate-hf-token.sh > token-validation.log 2>&1

# Check system info
curl --version
python3 --version
docker --version

# Check network connectivity
ping -c 3 huggingface.co
```

## ✅ Success Indicators

When everything works correctly, you should see:

```
🧪 Running HuggingFace Token Validation Tests

[STEP 1] Validating token syntax...
[SUCCESS] Token syntax is valid

[STEP 2] Testing HuggingFace API authentication...
[SUCCESS] Authentication successful
[INFO] Authenticated as: your-username

[STEP 3] Testing access to google/medgemma-4b-it...
[SUCCESS] Model access successful
[INFO] Model information:
[INFO]   Model ID: google/medgemma-4b-it
[INFO]   Downloads: 1,234
[INFO]   Gated: true

[STEP 4] Testing model file access...
[SUCCESS] Model files accessible
[INFO] Model contains 15 files

[STEP 5] Testing model download capability...
[SUCCESS] Model download test successful
[INFO] Successfully downloaded and validated config.json

📊 Validation Summary
[SUCCESS] Passed: 5/5 tests
[SUCCESS] ✅ All tests passed! Your token is ready for deployment.
```

After seeing this success message, you can proceed with deployment:

```bash
docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d
```
