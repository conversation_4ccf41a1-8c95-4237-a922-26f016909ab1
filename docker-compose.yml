version: '3.8'

services:
  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
      # Alternative build configurations:
      # For build args approach: dockerfile: docker/Dockerfile.medgemma.buildarg
      # For runtime download: dockerfile: docker/Dockerfile.medgemma.runtime
      #
      # Uncomment the following for build args approach:
      # args:
      #   HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
      #   HF_TOKEN: ${HUGGINGFACE_TOKEN}
    container_name: medgemma-api
    restart: unless-stopped
    environment:
      - MODEL_NAME=google/medgemma-4b-it
      - MAX_LENGTH=2048
      - TEMPERATURE=0.7
      - TOP_P=0.9
      - API_KEY=${API_KEY:-your-secret-api-key}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKERS=1
      - HOST=0.0.0.0
      - PORT=8000
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost,https://localhost}
      - UPLOAD_MAX_SIZE=10485760  # 10MB
      - CONVERSATION_HISTORY_LIMIT=50
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - model_cache:/app/model_cache
      - upload_data:/app/uploads
      - logs:/app/logs
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 14G
        reservations:
          memory: 12G
    networks:
      - medgemma-network

  nginx:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx
    container_name: medgemma-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./frontend:/usr/share/nginx/html:ro
      - ssl_certs:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - API_UPSTREAM=medgemma-api:8000
    depends_on:
      medgemma-api:
        condition: service_healthy
    networks:
      - medgemma-network

  certbot:
    image: certbot/certbot:latest
    container_name: medgemma-certbot
    volumes:
      - ssl_certs:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
      - EMAIL=${SSL_EMAIL}
    command: >
      sh -c "
      if [ ! -f /etc/letsencrypt/live/${DOMAIN_NAME}/fullchain.pem ]; then
        certbot certonly --webroot --webroot-path=/var/www/certbot 
        --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}
      fi
      "
    networks:
      - medgemma-network

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - medgemma-network

  monitoring:
    image: prom/prometheus:latest
    container_name: medgemma-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - medgemma-network

volumes:
  model_cache:
    driver: local
  upload_data:
    driver: local
  logs:
    driver: local
  ssl_certs:
    driver: local
  certbot_webroot:
    driver: local
  nginx_logs:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  medgemma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
