#!/bin/bash
# Build script using Docker BuildKit secrets (RECOMMENDED APPROACH)
# This is the most secure method for handling HuggingFace tokens during build

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with colors
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker supports BuildKit
    if ! docker buildx version >/dev/null 2>&1; then
        error "Docker BuildKit is not available. Please update Docker to a newer version."
        exit 1
    fi
    
    # Check if HuggingFace token file exists
    if [ ! -f ".hf_token" ]; then
        warn "HuggingFace token file (.hf_token) not found."
        echo
        echo "Please create a .hf_token file with your HuggingFace token:"
        echo "1. Get your token from: https://huggingface.co/settings/tokens"
        echo "2. Accept the MedGemma license at: https://huggingface.co/google/medgemma-4b-it"
        echo "3. Create the file: echo 'your_token_here' > .hf_token"
        echo "4. Secure the file: chmod 600 .hf_token"
        echo
        read -p "Do you want to create the .hf_token file now? (y/n): " create_token
        
        if [ "$create_token" = "y" ] || [ "$create_token" = "Y" ]; then
            read -p "Enter your HuggingFace token: " token
            echo "$token" > .hf_token
            chmod 600 .hf_token
            success "Token file created successfully"
        else
            error "Cannot proceed without HuggingFace token"
            exit 1
        fi
    fi
    
    # Validate token file
    if [ ! -s ".hf_token" ]; then
        error "HuggingFace token file is empty"
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Function to build the Docker image
build_image() {
    local image_name="${1:-medgemma-api}"
    local dockerfile="${2:-docker/Dockerfile.medgemma}"
    
    log "Building Docker image: $image_name"
    log "Using Dockerfile: $dockerfile"
    
    # Enable BuildKit
    export DOCKER_BUILDKIT=1
    
    # Build with secrets
    if docker buildx build \
        --secret id=huggingface_token,src=.hf_token \
        -f "$dockerfile" \
        -t "$image_name:latest" \
        -t "$image_name:$(date +%Y%m%d-%H%M%S)" \
        --progress=plain \
        .; then
        success "Docker image built successfully: $image_name"
    else
        error "Docker build failed"
        exit 1
    fi
}

# Function to verify the build
verify_build() {
    local image_name="${1:-medgemma-api}"
    
    log "Verifying build..."
    
    # Check if image exists
    if docker images "$image_name:latest" | grep -q "$image_name"; then
        success "Image verification passed"
        
        # Show image size
        local size=$(docker images "$image_name:latest" --format "table {{.Size}}" | tail -n 1)
        log "Image size: $size"
        
        # Show image layers (optional)
        log "Image layers:"
        docker history "$image_name:latest" --format "table {{.CreatedBy}}\t{{.Size}}" | head -10
    else
        error "Image verification failed"
        exit 1
    fi
}

# Function to clean up
cleanup() {
    log "Cleaning up..."
    
    # Remove intermediate images
    docker image prune -f >/dev/null 2>&1 || true
    
    success "Cleanup completed"
}

# Main function
main() {
    echo "=== Docker Build with BuildKit Secrets ==="
    echo "This script builds the MedGemma Docker image using BuildKit secrets"
    echo "for secure HuggingFace token handling during build."
    echo
    
    # Parse command line arguments
    local image_name="${1:-medgemma-api}"
    local dockerfile="${2:-docker/Dockerfile.medgemma}"
    
    # Run build process
    check_prerequisites
    build_image "$image_name" "$dockerfile"
    verify_build "$image_name"
    cleanup
    
    echo
    success "Build completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Test the image: docker run --rm -p 8000:8000 -e HUGGINGFACE_TOKEN=\$(cat .hf_token) $image_name"
    echo "2. Or use docker-compose: docker-compose -f docker-compose.dev.yml up"
    echo
    warn "Remember to set HUGGINGFACE_TOKEN environment variable when running the container"
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [image_name] [dockerfile]"
    echo
    echo "Arguments:"
    echo "  image_name  Name for the Docker image (default: medgemma-api)"
    echo "  dockerfile  Path to Dockerfile (default: docker/Dockerfile.medgemma)"
    echo
    echo "Examples:"
    echo "  $0                                    # Use defaults"
    echo "  $0 my-medgemma                       # Custom image name"
    echo "  $0 my-medgemma docker/Dockerfile.custom  # Custom name and dockerfile"
    exit 0
fi

# Run main function
main "$@"
