#!/usr/bin/env python3

"""
HuggingFace Token Validation Script (Python Version)
Advanced validation with detailed model information and license checking
"""

import os
import sys
import json
import requests
import argparse
from pathlib import Path
from typing import Optional, Dict, Any

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_status(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def print_header(message: str):
    print(f"{Colors.CYAN}{message}{Colors.NC}")

def print_step(step: int, message: str):
    print(f"{Colors.PURPLE}[STEP {step}]{Colors.NC} {message}")

class HuggingFaceValidator:
    def __init__(self, model_name: str = "google/medgemma-4b-it"):
        self.model_name = model_name
        self.api_base = "https://huggingface.co/api"
        self.timeout = 30
        self.session = requests.Session()
        
    def get_token_from_sources(self, token_arg: Optional[str] = None) -> Optional[str]:
        """Get HuggingFace token from various sources"""
        
        # 1. Command line argument
        if token_arg:
            print_status("Using token from command line argument")
            return token_arg
            
        # 2. Environment variable HUGGINGFACE_TOKEN
        if os.getenv('HUGGINGFACE_TOKEN'):
            token = os.getenv('HUGGINGFACE_TOKEN')
            if token != "hf_your_token_here_replace_with_actual_token":
                print_status("Using token from HUGGINGFACE_TOKEN environment variable")
                return token
                
        # 3. Environment variable HF_TOKEN
        if os.getenv('HF_TOKEN'):
            token = os.getenv('HF_TOKEN')
            if token != "hf_your_token_here_replace_with_actual_token":
                print_status("Using token from HF_TOKEN environment variable")
                return token
        
        # 4. .env.phase1 file
        env_files = ['.env.phase1', '.env']
        for env_file in env_files:
            if Path(env_file).exists():
                try:
                    with open(env_file, 'r') as f:
                        for line in f:
                            if line.startswith('HUGGINGFACE_TOKEN='):
                                token = line.split('=', 1)[1].strip().strip('"').strip("'")
                                if token and token != "hf_your_token_here_replace_with_actual_token":
                                    print_status(f"Using token from {env_file} file")
                                    return token
                except Exception as e:
                    print_warning(f"Could not read {env_file}: {e}")
        
        return None
    
    def validate_token_format(self, token: str) -> bool:
        """Validate HuggingFace token format"""
        print_step(1, "Validating token format...")
        
        if not token:
            print_error("Token is empty")
            return False
            
        if not token.startswith('hf_'):
            print_error("Token must start with 'hf_'")
            print_status(f"Your token starts with: {token[:10]}...")
            return False
            
        if len(token) != 37:
            print_error(f"Token must be exactly 37 characters (got {len(token)})")
            print_status("Expected format: hf_ + 34 alphanumeric characters")
            return False
            
        # Check if it contains only valid characters
        valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_')
        if not all(c in valid_chars for c in token):
            print_error("Token contains invalid characters")
            return False
            
        print_success("Token format is valid")
        return True
    
    def test_authentication(self, token: str) -> Dict[str, Any]:
        """Test basic HuggingFace authentication"""
        print_step(2, "Testing HuggingFace authentication...")
        
        headers = {'Authorization': f'Bearer {token}'}
        
        try:
            response = self.session.get(
                f"{self.api_base}/whoami",
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                user_info = response.json()
                print_success("Authentication successful")
                print_status(f"Authenticated as: {user_info.get('name', 'Unknown')}")
                print_status(f"User type: {user_info.get('type', 'Unknown')}")
                
                # Check token permissions
                orgs = user_info.get('orgs', [])
                if orgs:
                    print_status(f"Member of {len(orgs)} organizations")
                
                return {'success': True, 'user_info': user_info}
                
            elif response.status_code == 401:
                print_error("Authentication failed - Invalid token")
                return {'success': False, 'error': 'Invalid token'}
                
            elif response.status_code == 403:
                print_error("Authentication failed - Insufficient permissions")
                return {'success': False, 'error': 'Insufficient permissions'}
                
            else:
                print_error(f"Authentication failed - HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print_error(f"Network error during authentication: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_model_access(self, token: str) -> Dict[str, Any]:
        """Test access to the specific model"""
        print_step(3, f"Testing access to {self.model_name}...")
        
        headers = {'Authorization': f'Bearer {token}'}
        
        try:
            response = self.session.get(
                f"{self.api_base}/models/{self.model_name}",
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                model_info = response.json()
                print_success("Model access successful")
                
                # Display model information
                print_status("Model information:")
                print_status(f"  Model ID: {model_info.get('id', 'Unknown')}")
                print_status(f"  Downloads: {model_info.get('downloads', 'Unknown'):,}")
                print_status(f"  Likes: {model_info.get('likes', 'Unknown'):,}")
                print_status(f"  Private: {model_info.get('private', False)}")
                print_status(f"  Gated: {model_info.get('gated', False)}")
                
                # Check if model is gated
                if model_info.get('gated'):
                    print_warning("This is a gated model - license acceptance required")
                
                # Check model tags
                tags = model_info.get('tags', [])
                if tags:
                    print_status(f"  Tags: {', '.join(tags[:5])}")
                
                return {'success': True, 'model_info': model_info}
                
            elif response.status_code == 401:
                print_error("Model access failed - Authentication required")
                return {'success': False, 'error': 'Authentication required'}
                
            elif response.status_code == 403:
                print_error("Model access failed - Access denied")
                print_status("This usually means you haven't accepted the license agreement")
                return {'success': False, 'error': 'Access denied - license not accepted'}
                
            elif response.status_code == 404:
                print_error("Model not found")
                return {'success': False, 'error': 'Model not found'}
                
            else:
                print_error(f"Model access failed - HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print_error(f"Network error during model access test: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_model_files(self, token: str) -> Dict[str, Any]:
        """Test access to model files"""
        print_step(4, "Testing model file access...")
        
        headers = {'Authorization': f'Bearer {token}'}
        
        try:
            response = self.session.get(
                f"{self.api_base}/models/{self.model_name}/tree/main",
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                files = response.json()
                print_success("Model files accessible")
                
                if isinstance(files, list):
                    print_status(f"Model contains {len(files)} files")
                    
                    # Show some key files
                    key_files = ['config.json', 'pytorch_model.bin', 'tokenizer.json', 'model.safetensors']
                    found_files = []
                    
                    for file_info in files:
                        if isinstance(file_info, dict) and file_info.get('path') in key_files:
                            found_files.append(file_info['path'])
                    
                    if found_files:
                        print_status(f"  Key files found: {', '.join(found_files)}")
                
                return {'success': True, 'files': files}
                
            elif response.status_code in [401, 403]:
                print_error("Model files access denied")
                print_status("You can see the model but cannot access files")
                print_status("This indicates the license agreement hasn't been accepted")
                return {'success': False, 'error': 'Files access denied'}
                
            else:
                print_error(f"Model files test failed - HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print_error(f"Network error during file access test: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_download_capability(self, token: str) -> Dict[str, Any]:
        """Test actual download capability"""
        print_step(5, "Testing download capability...")
        
        headers = {'Authorization': f'Bearer {token}'}
        
        # Try to download config.json (small file)
        download_url = f"https://huggingface.co/{self.model_name}/resolve/main/config.json"
        
        try:
            response = self.session.get(
                download_url,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                try:
                    config = response.json()
                    print_success("Download test successful")
                    print_status("Successfully downloaded and parsed config.json")
                    
                    # Show some config info
                    if 'model_type' in config:
                        print_status(f"  Model type: {config['model_type']}")
                    if 'vocab_size' in config:
                        print_status(f"  Vocabulary size: {config['vocab_size']:,}")
                    
                    return {'success': True, 'config': config}
                    
                except json.JSONDecodeError:
                    print_warning("Downloaded file but JSON parsing failed")
                    return {'success': False, 'error': 'Invalid JSON response'}
                    
            elif response.status_code in [401, 403]:
                print_error("Download failed - Access denied")
                return {'success': False, 'error': 'Download access denied'}
                
            else:
                print_error(f"Download failed - HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.RequestException as e:
            print_error(f"Network error during download test: {e}")
            return {'success': False, 'error': str(e)}
    
    def validate_token(self, token: str) -> bool:
        """Run complete token validation"""
        print_header("🧪 Running HuggingFace Token Validation")
        print("")
        
        tests = [
            self.validate_token_format,
            self.test_authentication,
            self.test_model_access,
            self.test_model_files,
            self.test_download_capability
        ]
        
        passed = 0
        total = len(tests)
        
        for i, test in enumerate(tests):
            try:
                if i == 0:  # Format validation returns bool
                    result = test(token)
                    if result:
                        passed += 1
                    else:
                        break
                else:  # Other tests return dict
                    result = test(token)
                    if result.get('success'):
                        passed += 1
                    else:
                        break
                        
                print("")  # Add spacing between tests
                
            except Exception as e:
                print_error(f"Test {i+1} failed with exception: {e}")
                break
        
        # Summary
        print_header("📊 Validation Summary")
        print_success(f"Passed: {passed}/{total} tests")
        
        if passed == total:
            print_success("✅ All tests passed! Your token is ready for deployment.")
            print("")
            print_status("🚀 Next steps:")
            print("  • Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d")
            print("  • Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f")
            return True
        else:
            print_error("❌ Some tests failed. Please fix the issues before deployment.")
            return False

def show_help():
    """Show help instructions"""
    print_header("📋 How to Fix HuggingFace Token Issues")
    print("")
    
    print_status("1. Get a HuggingFace Token:")
    print("   • Go to: https://huggingface.co/settings/tokens")
    print("   • Click 'New token'")
    print("   • Choose 'Read' permissions (minimum required)")
    print("   • Copy the token (starts with hf_)")
    print("")
    
    print_status("2. Accept MedGemma License:")
    print("   • Go to: https://huggingface.co/google/medgemma-4b-it")
    print("   • Read and accept the license agreement")
    print("   • This is required for gated models like MedGemma")
    print("")
    
    print_status("3. Configure Your Token:")
    print("   • Add to .env.phase1: HUGGINGFACE_TOKEN=hf_your_token_here")
    print("   • Or set environment: export HUGGINGFACE_TOKEN=hf_your_token_here")
    print("")

def main():
    parser = argparse.ArgumentParser(
        description="Validate HuggingFace token for MedGemma-4b-it access"
    )
    parser.add_argument(
        'token', 
        nargs='?', 
        help='HuggingFace token (optional, will auto-detect if not provided)'
    )
    parser.add_argument(
        '--model', 
        default='google/medgemma-4b-it',
        help='Model to test access for (default: google/medgemma-4b-it)'
    )
    
    args = parser.parse_args()
    
    # Show banner
    print("")
    print_header("╔══════════════════════════════════════════════════════════════╗")
    print_header("║              HuggingFace Token Validator                    ║")
    print_header("║                 MedGemma-4b-it Access                       ║")
    print_header("╚══════════════════════════════════════════════════════════════╝")
    print("")
    
    validator = HuggingFaceValidator(args.model)
    
    # Get token
    token = validator.get_token_from_sources(args.token)
    
    if not token:
        print_error("No HuggingFace token found!")
        print_status("Please provide a token using one of these methods:")
        print_status("1. Command line: python3 scripts/validate-hf-token.py hf_your_token_here")
        print_status("2. Environment: export HUGGINGFACE_TOKEN=hf_your_token_here")
        print_status("3. .env.phase1 file: HUGGINGFACE_TOKEN=hf_your_token_here")
        print("")
        show_help()
        sys.exit(1)
    
    # Validate token
    success = validator.validate_token(token)
    
    if not success:
        print("")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
