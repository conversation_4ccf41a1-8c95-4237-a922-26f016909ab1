#!/bin/bash

# Interactive Phase Deployment Script
# Helps users choose and deploy the appropriate phase for their needs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_phase() {
    echo -e "${PURPLE}[PHASE $1]${NC} $2"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                    MedGemma AI Chat                          ║"
    print_header "║              Modular Deployment Assistant                    ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to display phase options
show_phase_options() {
    print_header "🚀 Choose Your Deployment Phase:"
    echo ""
    
    print_phase "1" "Standalone Core API (Development/Testing)"
    echo "   ✅ Complete AI chat functionality"
    echo "   ✅ Direct API access on port 8000"
    echo "   ✅ Basic HTML frontend included"
    echo "   ✅ Redis conversation storage"
    echo "   🎯 Perfect for: Development, testing, quick demos"
    echo ""
    
    print_phase "2" "Production Nginx Setup (HTTP)"
    echo "   ✅ All Phase 1 features"
    echo "   ✅ Professional reverse proxy"
    echo "   ✅ Static file optimization"
    echo "   ✅ Basic security headers"
    echo "   🎯 Perfect for: Staging, internal production"
    echo ""
    
    print_phase "3" "SSL/TLS Security (HTTPS)"
    echo "   ✅ All Phase 1 & 2 features"
    echo "   ✅ SSL/TLS encryption"
    echo "   ✅ Let's Encrypt certificates"
    echo "   ✅ Security headers (HSTS, CSP)"
    echo "   🎯 Perfect for: Public production deployment"
    echo ""
    
    print_phase "4" "Enterprise Features (Full Production)"
    echo "   ✅ All Phase 1, 2 & 3 features"
    echo "   ✅ Prometheus monitoring"
    echo "   ✅ Grafana dashboards"
    echo "   ✅ Automated backups"
    echo "   ✅ Advanced security"
    echo "   🎯 Perfect for: Enterprise, mission-critical"
    echo ""
}

# Function to get user choice
get_user_choice() {
    while true; do
        echo -n "Enter your choice (1-4): "
        read -r choice
        
        case $choice in
            1|2|3|4)
                return $choice
                ;;
            *)
                print_error "Invalid choice. Please enter 1, 2, 3, or 4."
                ;;
        esac
    done
}

# Function to check prerequisites
check_prerequisites() {
    local phase=$1
    print_status "Checking prerequisites for Phase $phase..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        print_status "Install Docker: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
        return 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        print_status "Install Docker Compose: sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose && sudo chmod +x /usr/local/bin/docker-compose"
        return 1
    fi
    
    # Check disk space
    local available_gb=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $available_gb -lt 20 ]]; then
        print_warning "Less than 20GB available disk space. MedGemma requires ~8GB for the model."
        echo -n "Continue anyway? (y/N): "
        read -r continue_choice
        if [[ ! $continue_choice =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    # Phase-specific checks
    case $phase in
        3|4)
            print_status "Phase $phase requires a domain name for SSL certificates"
            echo -n "Do you have a domain name pointing to this server? (y/N): "
            read -r domain_choice
            if [[ ! $domain_choice =~ ^[Yy]$ ]]; then
                print_warning "Phase $phase requires a domain name for SSL certificates"
                print_status "You can:"
                print_status "1. Configure a domain name first"
                print_status "2. Deploy Phase 2 now and upgrade later"
                echo -n "Continue with Phase $phase anyway? (y/N): "
                read -r continue_anyway
                if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
                    return 1
                fi
            fi
            ;;
    esac
    
    print_success "Prerequisites check passed"
    return 0
}

# Function to setup environment
setup_environment() {
    local phase=$1
    local env_file=".env.phase${phase}"
    
    print_status "Setting up environment for Phase $phase..."
    
    if [[ ! -f "$env_file" ]]; then
        if [[ -f "${env_file}.example" ]]; then
            cp "${env_file}.example" "$env_file"
            print_success "Created $env_file from template"
        else
            print_error "Template file ${env_file}.example not found"
            return 1
        fi
    fi
    
    # Check if critical variables are set
    source "$env_file"
    
    local needs_config=false
    
    if [[ -z "$API_KEY" || "$API_KEY" == "your-super-secret-api-key-32-chars-minimum-change-this" ]]; then
        print_warning "API_KEY needs to be configured"
        needs_config=true
    fi
    
    if [[ -z "$HUGGINGFACE_TOKEN" || "$HUGGINGFACE_TOKEN" == "hf_your_token_here_replace_with_actual_token" ]]; then
        print_warning "HUGGINGFACE_TOKEN needs to be configured"
        needs_config=true
    fi
    
    if [[ -z "$REDIS_PASSWORD" || "$REDIS_PASSWORD" == "your-secure-redis-password-change-this" ]]; then
        print_warning "REDIS_PASSWORD needs to be configured"
        needs_config=true
    fi
    
    if [[ $phase -ge 3 ]]; then
        if [[ -z "$DOMAIN_NAME" || "$DOMAIN_NAME" == "your-domain.com" ]]; then
            print_warning "DOMAIN_NAME needs to be configured for Phase $phase"
            needs_config=true
        fi
        
        if [[ -z "$SSL_EMAIL" || "$SSL_EMAIL" == "<EMAIL>" ]]; then
            print_warning "SSL_EMAIL needs to be configured for Phase $phase"
            needs_config=true
        fi
    fi
    
    if [[ $needs_config == true ]]; then
        print_error "Configuration required before deployment"
        print_status "Please edit $env_file with your settings:"
        print_status "  nano $env_file"
        echo ""
        print_status "Required settings:"
        print_status "  • API_KEY: Strong API key (32+ characters)"
        print_status "  • HUGGINGFACE_TOKEN: Your HuggingFace token"
        print_status "  • REDIS_PASSWORD: Secure Redis password"
        if [[ $phase -ge 3 ]]; then
            print_status "  • DOMAIN_NAME: Your domain name"
            print_status "  • SSL_EMAIL: Your email for SSL certificates"
        fi
        echo ""
        echo -n "Open editor now? (y/N): "
        read -r edit_choice
        if [[ $edit_choice =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} "$env_file"
        fi
        return 1
    fi
    
    print_success "Environment configuration looks good"
    return 0
}

# Function to deploy phase
deploy_phase() {
    local phase=$1
    
    print_status "🚀 Deploying Phase $phase..."
    
    # Setup directories
    if [[ -f "scripts/setup-phase${phase}-directories.sh" ]]; then
        print_status "Setting up directories..."
        ./scripts/setup-phase${phase}-directories.sh
    fi
    
    # Stop any running services
    for p in 1 2 3 4; do
        if docker-compose -f "docker-compose.phase${p}.yml" ps 2>/dev/null | grep -q "Up"; then
            print_status "Stopping Phase $p services..."
            docker-compose -f "docker-compose.phase${p}.yml" down
        fi
    done
    
    # Start Phase services
    print_status "Starting Phase $phase services..."
    docker-compose -f "docker-compose.phase${phase}.yml" --env-file ".env.phase${phase}" up -d
    
    # Wait for services to start
    print_status "Waiting for services to start..."
    sleep 30
    
    # Phase-specific post-deployment steps
    case $phase in
        3|4)
            if [[ -f "scripts/setup-ssl-phase3.sh" ]]; then
                print_status "Setting up SSL certificates..."
                echo -n "Generate SSL certificates now? (y/N): "
                read -r ssl_choice
                if [[ $ssl_choice =~ ^[Yy]$ ]]; then
                    ./scripts/setup-ssl-phase3.sh
                fi
            fi
            ;;
    esac
    
    print_success "Phase $phase deployment completed!"
}

# Function to show deployment summary
show_deployment_summary() {
    local phase=$1
    
    # Get access URL
    local ec2_ip=$(curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")
    local access_url
    
    case $phase in
        1)
            access_url="http://${ec2_ip}:8000"
            ;;
        2)
            access_url="http://${ec2_ip}"
            ;;
        3|4)
            if [[ -f ".env.phase${phase}" ]]; then
                source ".env.phase${phase}"
                access_url="https://${DOMAIN_NAME:-$ec2_ip}"
            else
                access_url="https://${ec2_ip}"
            fi
            ;;
    esac
    
    echo ""
    print_success "🎉 Phase $phase Deployment Complete!"
    echo ""
    print_header "📊 Deployment Summary:"
    print_phase "$phase" "Phase $phase is now running"
    print_status "Access URL: $access_url"
    print_status "API Documentation: $access_url/docs"
    print_status "Health Check: $access_url/health"
    
    if [[ $phase -eq 4 ]]; then
        print_status "Monitoring:"
        print_status "  • Prometheus: http://${ec2_ip}:9090"
        print_status "  • Grafana: http://${ec2_ip}:3000"
    fi
    
    echo ""
    print_header "🔧 Management Commands:"
    echo "  • View logs: docker-compose -f docker-compose.phase${phase}.yml logs -f"
    echo "  • Stop services: docker-compose -f docker-compose.phase${phase}.yml down"
    echo "  • Restart services: docker-compose -f docker-compose.phase${phase}.yml restart"
    echo "  • Test deployment: ./scripts/test-all-phases.sh"
    
    echo ""
    print_header "📁 Important Files:"
    echo "  • Configuration: .env.phase${phase}"
    echo "  • Data directory: ./data/phase${phase}/"
    echo "  • Docker Compose: docker-compose.phase${phase}.yml"
    
    if [[ $phase -lt 4 ]]; then
        echo ""
        print_header "🚀 Upgrade Options:"
        local next_phase=$((phase + 1))
        echo "  • Upgrade to Phase $next_phase: ./scripts/migrate-phase${phase}-to-phase${next_phase}.sh"
    fi
}

# Main function
main() {
    show_banner
    
    # Check if specific phase was requested
    if [[ $# -gt 0 ]]; then
        case $1 in
            1|2|3|4)
                local phase=$1
                print_status "Deploying Phase $phase as requested..."
                ;;
            "--help"|"-h")
                echo "Usage: $0 [phase_number]"
                echo "  phase_number: 1, 2, 3, or 4 (optional, interactive if not specified)"
                echo ""
                show_phase_options
                exit 0
                ;;
            *)
                print_error "Invalid phase: $1"
                print_status "Valid phases: 1, 2, 3, 4"
                exit 1
                ;;
        esac
    else
        # Interactive mode
        show_phase_options
        get_user_choice
        local phase=$?
    fi
    
    print_phase "$phase" "Selected Phase $phase deployment"
    
    # Check prerequisites
    if ! check_prerequisites $phase; then
        print_error "Prerequisites check failed"
        exit 1
    fi
    
    # Setup environment
    if ! setup_environment $phase; then
        print_error "Environment setup incomplete"
        print_status "Please configure .env.phase${phase} and run this script again"
        exit 1
    fi
    
    # Deploy
    deploy_phase $phase
    
    # Test deployment
    print_status "Testing deployment..."
    if [[ -f "scripts/test-all-phases.sh" ]]; then
        ./scripts/test-all-phases.sh $phase
    fi
    
    # Show summary
    show_deployment_summary $phase
    
    echo ""
    print_success "✅ Deployment completed successfully!"
}

# Run main function
main "$@"
