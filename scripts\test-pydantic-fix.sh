#!/bin/bash

# Test script to verify the Pydantic CORS_ORIGINS fix
# This script tests the configuration without running the full deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running test: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "✅ $test_name: PASSED"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "❌ $test_name: FAILED"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test configuration syntax
test_config_syntax() {
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              Pydantic Configuration Fix Test                ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    # Test 1: Python syntax validation
    run_test "Python syntax validation" \
        "python -c \"import ast; ast.parse(open('app/config.py').read())\""
    
    # Test 2: Check for Pydantic v2 syntax
    if grep -q "field_validator" app/config.py; then
        print_success "✅ Uses Pydantic v2 field_validator syntax"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_error "❌ Missing Pydantic v2 field_validator syntax"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 3: Check for model_config
    if grep -q "model_config" app/config.py; then
        print_success "✅ Uses Pydantic v2 model_config syntax"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_error "❌ Missing Pydantic v2 model_config syntax"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 4: Check for CORS_ORIGINS special case handling
    if grep -q "if v.strip() == \"\\*\":" app/config.py; then
        print_success "✅ CORS_ORIGINS validator handles '*' special case"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_error "❌ CORS_ORIGINS validator missing '*' special case handling"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 5: Check for ALLOWED_HOSTS special case handling
    if grep -A 10 "parse_allowed_hosts" app/config.py | grep -q "if v.strip() == \"\\*\":"; then
        print_success "✅ ALLOWED_HOSTS validator handles '*' special case"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_error "❌ ALLOWED_HOSTS validator missing '*' special case handling"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 6: Check environment files format
    local env_files=(".env.phase1.example" ".env.development")
    for env_file in "${env_files[@]}"; do
        if [[ -f "$env_file" ]]; then
            if grep -q "CORS_ORIGINS=\\*" "$env_file"; then
                print_success "✅ $env_file has correct CORS_ORIGINS format"
                TESTS_PASSED=$((TESTS_PASSED + 1))
            else
                print_warning "⚠️  $env_file doesn't use CORS_ORIGINS=* format"
            fi
        fi
    done
}

# Function to test Docker build (optional)
test_docker_build() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                   Docker Build Test                         ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    if command -v docker >/dev/null 2>&1; then
        print_status "Testing Docker build with Pydantic fix..."
        
        # Create a test Dockerfile that just validates the configuration
        cat > Dockerfile.test-pydantic << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install minimal requirements for testing
RUN pip install pydantic==2.5.0 pydantic-settings==2.1.0

# Copy app directory
COPY app/ ./app/

# Create test script
RUN echo 'import os' > test_config.py && \
    echo 'os.environ["CORS_ORIGINS"] = "*"' >> test_config.py && \
    echo 'os.environ["ALLOWED_HOSTS"] = "*"' >> test_config.py && \
    echo 'os.environ["API_KEY"] = "test-api-key-32-characters-long"' >> test_config.py && \
    echo 'from app.config import Settings' >> test_config.py && \
    echo 'settings = Settings()' >> test_config.py && \
    echo 'print(f"CORS_ORIGINS: {settings.CORS_ORIGINS}")' >> test_config.py && \
    echo 'print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")' >> test_config.py && \
    echo 'print("✅ Pydantic configuration test passed!")' >> test_config.py

# Test configuration loading
RUN python test_config.py
EOF
        
        if docker build -f Dockerfile.test-pydantic -t test-pydantic-config . >/dev/null 2>&1; then
            print_success "✅ Docker build test passed"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            
            # Clean up
            docker rmi test-pydantic-config >/dev/null 2>&1 || true
            rm -f Dockerfile.test-pydantic
        else
            print_error "❌ Docker build test failed"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            
            print_status "Build log:"
            docker build -f Dockerfile.test-pydantic -t test-pydantic-config . 2>&1 | tail -10
            rm -f Dockerfile.test-pydantic
        fi
    else
        print_warning "⚠️  Docker not available, skipping Docker build test"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Test script to verify the Pydantic CORS_ORIGINS configuration fix"
    echo ""
    echo "Options:"
    echo "  --docker, -d     Include Docker build test"
    echo "  --help, -h       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0               # Run basic configuration tests"
    echo "  $0 --docker      # Run all tests including Docker build"
}

# Main function
main() {
    local include_docker=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --docker|-d)
                include_docker=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Run tests
    test_config_syntax
    
    if [[ $include_docker == true ]]; then
        test_docker_build
    fi
    
    # Show results
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                        Test Summary                         ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    print_status "Total tests run: $total_tests"
    print_success "Tests passed: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        print_error "Tests failed: $TESTS_FAILED"
        echo ""
        print_error "💥 Some tests failed. Please review the issues above."
        return 1
    else
        print_success "Tests failed: $TESTS_FAILED"
        echo ""
        print_success "🎉 All tests passed! The Pydantic CORS_ORIGINS fix is working correctly."
        echo ""
        print_status "Key improvements:"
        print_status "• Updated to Pydantic v2 syntax (field_validator, model_config)"
        print_status "• Added special case handling for CORS_ORIGINS=* and ALLOWED_HOSTS=*"
        print_status "• Fixed JSON parsing issues with List[str] fields"
        print_status "• Phase 1 deployment should now work without configuration errors"
        return 0
    fi
}

# Run main function
main "$@"
