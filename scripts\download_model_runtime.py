#!/usr/bin/env python3
"""
Runtime script to download MedGemma model if not already cached
This script is called when the container starts if the model wasn't downloaded during build
"""

import os
import sys
import logging
import time
from pathlib import Path
from transformers import AutoProcessor, AutoModelForImageTextToText
from huggingface_hub import login, HfApi
import torch

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def is_model_cached(cache_dir: str, model_name: str) -> bool:
    """Check if model is already cached"""
    cache_path = Path(cache_dir)
    if not cache_path.exists():
        return False
    
    # Check for key model files
    model_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json",
        "tokenizer_config.json"
    ]
    
    # Look for any model files in subdirectories
    for model_file in model_files:
        if list(cache_path.rglob(model_file)):
            logger.info(f"Found cached model files in {cache_dir}")
            return True
    
    return False


def check_huggingface_token():
    """Check if HuggingFace token is available from environment variables"""
    token = os.getenv('HUGGINGFACE_TOKEN') or os.getenv('HF_TOKEN')

    if not token:
        logger.error("No HuggingFace token found!")
        logger.error("Please set HUGGINGFACE_TOKEN environment variable.")
        logger.error("")
        logger.error("To set the token:")
        logger.error("1. Add to .env file: HUGGINGFACE_TOKEN=hf_your_token_here")
        logger.error("2. Or export directly: export HUGGINGFACE_TOKEN=hf_your_token_here")
        logger.error("")
        logger.error("Get your token from: https://huggingface.co/settings/tokens")
        logger.error("Accept MedGemma license: https://huggingface.co/google/medgemma-4b-it")
        return None

    # Validate token format
    if not token.startswith('hf_'):
        logger.warning("Token doesn't start with 'hf_' - this may not be a valid HuggingFace token")

    try:
        login(token=token, add_to_git_credential=False)
        logger.info("Successfully authenticated with HuggingFace")
        return token
    except Exception as e:
        logger.error(f"Failed to authenticate with HuggingFace: {e}")
        logger.error("Please check your token and ensure you have access to the model")
        logger.error("Make sure you've accepted the MedGemma license agreement")
        return None


def check_model_access(model_name: str, token: str = None):
    """Check if we have access to the model"""
    try:
        api = HfApi(token=token)
        model_info = api.model_info(model_name)
        logger.info(f"Model {model_name} is accessible")
        return True
    except Exception as e:
        logger.error(f"Cannot access model {model_name}: {e}")
        logger.error("You may need to:")
        logger.error("1. Accept the model's license agreement on HuggingFace")
        logger.error("2. Set a valid HUGGINGFACE_TOKEN environment variable")
        logger.error("3. Ensure you have access to the model")
        return False


def download_model_with_retry(model_name: str, cache_dir: str, token: str = None, max_retries: int = 3):
    """Download model with retry logic"""
    for attempt in range(max_retries):
        try:
            logger.info(f"Download attempt {attempt + 1}/{max_retries}")
            
            # Check model access first
            if not check_model_access(model_name, token):
                return False
            
            # Create cache directory
            Path(cache_dir).mkdir(parents=True, exist_ok=True)
            
            # Download processor
            logger.info("Downloading processor...")
            processor = AutoProcessor.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                trust_remote_code=True,
                token=token
            )
            logger.info("Processor downloaded successfully")
            
            # Download model
            logger.info("Downloading model...")
            model = AutoModelForImageTextToText.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                torch_dtype=torch.float32,  # Use float32 for CPU
                trust_remote_code=True,
                token=token,
                low_cpu_mem_usage=True
            )
            logger.info("Model downloaded successfully")
            
            # Test model loading
            logger.info("Testing model loading...")
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"Using device: {device}")
            
            if device == "cpu":
                model = model.to(device)
            
            logger.info("Model test successful")
            
            # Clean up memory
            del model
            del processor
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            return True
            
        except Exception as e:
            logger.error(f"Download attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error("All download attempts failed")
                return False
    
    return False


def main():
    """Main function"""
    logger.info("Checking model cache status...")
    
    # Check if model is already cached
    if is_model_cached(settings.MODEL_CACHE_DIR, settings.MODEL_NAME):
        logger.info("Model is already cached. Skipping download.")
        return True
    
    logger.info("Model not found in cache. Starting download...")
    
    # Check HuggingFace token
    token = check_huggingface_token()
    if not token:
        logger.error("Cannot proceed without HuggingFace token")
        return False
    
    # Download model with retry
    success = download_model_with_retry(
        model_name=settings.MODEL_NAME,
        cache_dir=settings.MODEL_CACHE_DIR,
        token=token,
        max_retries=3
    )
    
    if success:
        logger.info("Model download completed successfully!")
        logger.info(f"Cache location: {settings.MODEL_CACHE_DIR}")
        return True
    else:
        logger.error("Model download failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
