#!/bin/bash

# HuggingFace Token Validation Script (CLI Version)
# Uses official HuggingFace CLI commands for validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${PURPLE}[STEP $1]${NC} $2"
}

# Configuration
MODEL_NAME="google/medgemma-4b-it"
TEST_TIMEOUT=60

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              HuggingFace Token Validator                    ║"
    print_header "║                 (Official CLI Version)                      ║"
    print_header "║                 MedGemma-4b-it Access                       ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_step "0" "Checking prerequisites..."
    
    local prereq_issues=0
    
    # Check if huggingface-cli is installed
    if command -v huggingface-cli &> /dev/null; then
        local hf_version=$(huggingface-cli --version 2>/dev/null | head -1 || echo "Unknown version")
        print_success "HuggingFace CLI found: $hf_version"
    else
        print_error "HuggingFace CLI not found"
        print_status "Install with: pip install huggingface_hub[cli]"
        print_status "Or: pip install --upgrade huggingface_hub"
        ((prereq_issues++))
    fi
    
    # Check Python (usually required for HF CLI)
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version 2>/dev/null || echo "Unknown")
        print_success "Python found: $python_version"
    else
        print_warning "Python3 not found - may be required for HuggingFace CLI"
    fi
    
    echo ""
    if [[ $prereq_issues -eq 0 ]]; then
        print_success "Prerequisites check passed"
        return 0
    else
        print_error "$prereq_issues prerequisite issues found"
        return 1
    fi
}

# Function to get token from various sources and set environment
setup_token_environment() {
    local token=""
    local token_source=""
    
    # Check command line argument first
    if [[ $# -gt 0 && -n "$1" ]]; then
        token="$1"
        token_source="command line argument"
    # Check environment variable
    elif [[ -n "$HUGGINGFACE_TOKEN" ]]; then
        token="$HUGGINGFACE_TOKEN"
        token_source="HUGGINGFACE_TOKEN environment variable"
    # Check .env.phase1 file
    elif [[ -f ".env.phase1" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env.phase1 | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            token_source=".env.phase1 file"
        fi
    # Check .env file
    elif [[ -f ".env" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            token_source=".env file"
        fi
    # Check HF_TOKEN as fallback
    elif [[ -n "$HF_TOKEN" ]]; then
        token="$HF_TOKEN"
        token_source="HF_TOKEN environment variable"
    fi
    
    if [[ -z "$token" ]]; then
        print_error "No HuggingFace token found!"
        print_status "Please provide a token using one of these methods:"
        print_status "1. Command line: $0 hf_your_token_here"
        print_status "2. Environment: export HUGGINGFACE_TOKEN=hf_your_token_here"
        print_status "3. .env.phase1 file: HUGGINGFACE_TOKEN=hf_your_token_here"
        return 1
    fi
    
    # Set environment variables for HuggingFace CLI
    export HUGGINGFACE_TOKEN="$token"
    export HF_TOKEN="$token"
    
    print_status "Using token from $token_source"
    return 0
}

# Function to validate token syntax
validate_token_syntax() {
    print_step "1" "Validating token syntax..."
    
    local token="$HUGGINGFACE_TOKEN"
    
    # Check if token is empty
    if [[ -z "$token" ]]; then
        print_error "Token is empty"
        return 1
    fi
    
    # Check token format (should start with hf_)
    if [[ ! "$token" =~ ^hf_[a-zA-Z0-9]{34}$ ]]; then
        print_error "Invalid token format"
        print_status "Expected format: hf_ followed by 34 alphanumeric characters"
        print_status "Your token: ${token:0:10}... (${#token} characters)"
        
        if [[ ! "$token" =~ ^hf_ ]]; then
            print_status "Token should start with 'hf_'"
        fi
        
        if [[ ${#token} -ne 37 ]]; then
            print_status "Token should be exactly 37 characters long (hf_ + 34 chars)"
        fi
        
        return 1
    fi
    
    print_success "Token syntax is valid"
    return 0
}

# Function to test HuggingFace CLI authentication
test_hf_cli_authentication() {
    print_step "2" "Testing HuggingFace CLI authentication..."
    
    # Use huggingface-cli whoami command
    local whoami_output
    local exit_code
    
    # Capture both output and exit code
    whoami_output=$(huggingface-cli whoami 2>&1)
    exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "Authentication successful"
        
        # Extract username from output
        local username=$(echo "$whoami_output" | grep -E "username:|name:" | head -1 | cut -d':' -f2 | xargs || echo "Unknown")
        if [[ "$username" != "Unknown" && -n "$username" ]]; then
            print_status "Authenticated as: $username"
        fi
        
        # Extract email if available
        local email=$(echo "$whoami_output" | grep -E "email:" | head -1 | cut -d':' -f2 | xargs || echo "")
        if [[ -n "$email" ]]; then
            print_status "Email: $email"
        fi
        
        # Check for organizations
        if echo "$whoami_output" | grep -q "orgs:"; then
            local orgs=$(echo "$whoami_output" | grep -A 10 "orgs:" | grep -E "^\s*-" | wc -l)
            if [[ $orgs -gt 0 ]]; then
                print_status "Member of $orgs organizations"
            fi
        fi
        
        return 0
    else
        print_error "Authentication failed"
        
        # Parse common error messages
        if echo "$whoami_output" | grep -qi "invalid.*token\|unauthorized\|401"; then
            print_status "Token is invalid or expired"
            print_status "Generate a new token at: https://huggingface.co/settings/tokens"
        elif echo "$whoami_output" | grep -qi "network\|connection\|timeout"; then
            print_status "Network connectivity issue"
            print_status "Check your internet connection"
        else
            print_status "Error details: $whoami_output"
        fi
        
        return 1
    fi
}

# Function to test model access using CLI
test_model_access_cli() {
    print_step "3" "Testing access to $MODEL_NAME..."
    
    # Use huggingface-cli to get model info
    local model_info_output
    local exit_code
    
    # Try to get model information
    model_info_output=$(huggingface-cli scan-cache --repo "$MODEL_NAME" 2>&1 || huggingface-cli repo info "$MODEL_NAME" 2>&1)
    exit_code=$?
    
    # Alternative approach: try to list model files
    if [[ $exit_code -ne 0 ]]; then
        model_info_output=$(timeout $TEST_TIMEOUT huggingface-cli download "$MODEL_NAME" --repo-type model --dry-run 2>&1)
        exit_code=$?
    fi
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "Model access successful"
        
        # Check if model is gated
        if echo "$model_info_output" | grep -qi "gated\|license\|agreement"; then
            print_warning "This is a gated model - license acceptance required"
        fi
        
        return 0
    else
        print_error "Model access failed"
        
        # Parse common error messages
        if echo "$model_info_output" | grep -qi "not.*found\|404"; then
            print_status "Model not found: $MODEL_NAME"
        elif echo "$model_info_output" | grep -qi "access.*denied\|forbidden\|403"; then
            print_status "Access denied - you may need to:"
            print_status "  1. Accept the model's license agreement"
            print_status "  2. Request access to the gated model"
            print_status "  3. Ensure your token has proper permissions"
        elif echo "$model_info_output" | grep -qi "unauthorized\|401"; then
            print_status "Authentication failed - check your token"
        else
            print_status "Error details: $model_info_output"
        fi
        
        return 1
    fi
}

# Function to test model download using CLI
test_model_download_cli() {
    print_step "4" "Testing model download capability..."
    
    # Create temporary directory for test download
    local temp_dir=$(mktemp -d)
    local download_output
    local exit_code
    
    print_status "Testing download of config.json to temporary directory..."
    
    # Try to download just the config.json file
    download_output=$(timeout $TEST_TIMEOUT huggingface-cli download "$MODEL_NAME" config.json --local-dir "$temp_dir" --quiet 2>&1)
    exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "Model download test successful"
        
        # Check if file was actually downloaded
        if [[ -f "$temp_dir/config.json" ]]; then
            print_status "Successfully downloaded config.json"
            
            # Try to extract model information from config
            if command -v python3 &> /dev/null; then
                local model_type=$(python3 -c "
import json
try:
    with open('$temp_dir/config.json', 'r') as f:
        config = json.load(f)
    print(f\"Model type: {config.get('model_type', 'Unknown')}\")
    print(f\"Vocab size: {config.get('vocab_size', 'Unknown')}\")
except:
    print('Could not parse config.json')
" 2>/dev/null)
                if [[ -n "$model_type" ]]; then
                    print_status "$model_type"
                fi
            fi
        else
            print_warning "Download completed but file not found"
        fi
        
        # Cleanup
        rm -rf "$temp_dir"
        return 0
    else
        print_error "Model download failed"
        
        # Parse common error messages
        if echo "$download_output" | grep -qi "access.*denied\|forbidden\|403"; then
            print_status "Download access denied"
            print_status "You may need to accept the license agreement at:"
            print_status "https://huggingface.co/$MODEL_NAME"
        elif echo "$download_output" | grep -qi "not.*found\|404"; then
            print_status "File not found - model may not have config.json"
        elif echo "$download_output" | grep -qi "timeout"; then
            print_status "Download timed out - network or server issue"
        else
            print_status "Error details: $download_output"
        fi
        
        # Cleanup
        rm -rf "$temp_dir"
        return 1
    fi
}

# Function to provide help and instructions
show_help_instructions() {
    echo ""
    print_header "📋 How to Fix HuggingFace Token Issues"
    echo ""
    
    print_status "1. Install HuggingFace CLI:"
    echo "   • pip install huggingface_hub[cli]"
    echo "   • Or: pip install --upgrade huggingface_hub"
    echo ""
    
    print_status "2. Get a HuggingFace Token:"
    echo "   • Go to: https://huggingface.co/settings/tokens"
    echo "   • Click 'New token'"
    echo "   • Choose 'Read' permissions (minimum required)"
    echo "   • Copy the token (starts with hf_)"
    echo ""
    
    print_status "3. Accept MedGemma License:"
    echo "   • Go to: https://huggingface.co/$MODEL_NAME"
    echo "   • Read and accept the license agreement"
    echo "   • This is required for gated models like MedGemma"
    echo ""
    
    print_status "4. Configure Your Token:"
    echo "   • Method 1 - Environment file:"
    echo "     echo 'HUGGINGFACE_TOKEN=hf_your_token_here' >> .env.phase1"
    echo ""
    echo "   • Method 2 - Environment variable:"
    echo "     export HUGGINGFACE_TOKEN=hf_your_token_here"
    echo ""
    echo "   • Method 3 - HuggingFace CLI login:"
    echo "     huggingface-cli login"
    echo ""
    
    print_status "5. Verify Token:"
    echo "   • Run this script again: $0"
    echo "   • Or test manually: huggingface-cli whoami"
    echo ""
}

# Function to run all validation tests
run_validation_tests() {
    local test_count=0
    local passed_count=0
    
    print_header "🧪 Running HuggingFace CLI Token Validation"
    echo ""
    
    # Test 0: Prerequisites
    ((test_count++))
    if check_prerequisites; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 1: Token syntax
    ((test_count++))
    if validate_token_syntax; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 2: CLI authentication
    ((test_count++))
    if test_hf_cli_authentication; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 3: Model access
    ((test_count++))
    if test_model_access_cli; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 4: Model download
    ((test_count++))
    if test_model_download_cli; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Summary
    print_header "📊 Validation Summary"
    print_success "Passed: $passed_count/$test_count tests"
    
    if [[ $passed_count -eq $test_count ]]; then
        print_success "✅ All tests passed! Your token is ready for deployment."
        echo ""
        print_status "🚀 Next steps:"
        echo "  • Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d"
        echo "  • Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f"
        echo "  • Test deployment: ./scripts/test-phase1.sh"
        return 0
    else
        print_error "❌ Some tests failed. Please fix the issues before deployment."
        return 1
    fi
}

# Main function
main() {
    show_banner
    
    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [token]"
        echo ""
        echo "HuggingFace token validation using official CLI commands"
        echo ""
        echo "Arguments:"
        echo "  token    Optional HuggingFace token (if not provided, will look in env files)"
        echo ""
        echo "Examples:"
        echo "  $0                           # Auto-detect token from environment"
        echo "  $0 hf_your_token_here       # Test specific token"
        echo ""
        show_help_instructions
        exit 0
    fi
    
    # Setup token environment
    if ! setup_token_environment "$@"; then
        echo ""
        show_help_instructions
        exit 1
    fi
    
    # Run validation tests
    if run_validation_tests; then
        exit 0
    else
        echo ""
        show_help_instructions
        exit 1
    fi
}

# Run main function
main "$@"
