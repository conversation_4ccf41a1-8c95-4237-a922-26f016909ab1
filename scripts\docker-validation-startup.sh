#!/bin/bash

# Docker-based HuggingFace Token Validation Startup Script
# Runs validation inside the same environment as MedGemma deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${Colors.NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check environment
check_environment() {
    print_status "Checking Docker validation environment..."
    
    # Check Python
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version)
        print_success "Python: $python_version"
    else
        print_error "Python3 not found"
        return 1
    fi
    
    # Check HuggingFace Hub
    if python3 -c "import huggingface_hub; print(f'HuggingFace Hub: {huggingface_hub.__version__}')" 2>/dev/null; then
        print_success "HuggingFace Hub library available"
    else
        print_error "HuggingFace Hub library not found"
        return 1
    fi
    
    # Check cache directory permissions
    if [[ -w "/app/model_cache" ]]; then
        print_success "Cache directory is writable"
    else
        print_warning "Cache directory not writable - using temporary directory"
    fi
    
    # Check network connectivity
    if curl -s --connect-timeout 10 https://huggingface.co > /dev/null; then
        print_success "Network connectivity to HuggingFace: OK"
    else
        print_error "Cannot reach HuggingFace - check network connectivity"
        return 1
    fi
    
    return 0
}

# Function to check token environment variables
check_token_env() {
    print_status "Checking token environment variables..."
    
    local token_found=false
    
    if [[ -n "$HUGGINGFACE_TOKEN" ]]; then
        print_success "HUGGINGFACE_TOKEN is set"
        token_found=true
    fi
    
    if [[ -n "$HF_TOKEN" ]]; then
        print_success "HF_TOKEN is set"
        token_found=true
    fi
    
    if [[ $token_found == false ]]; then
        print_error "No HuggingFace token found in environment variables"
        print_status "Required: HUGGINGFACE_TOKEN or HF_TOKEN"
        return 1
    fi
    
    return 0
}

# Function to run the validation
run_validation() {
    print_status "Starting Docker-based token validation..."
    
    # Run the Python validation script
    if python3 /app/validation/validate-hf-token-docker.py; then
        print_success "✅ Docker validation completed successfully!"
        return 0
    else
        print_error "❌ Docker validation failed!"
        return 1
    fi
}

# Function to show usage information
show_usage() {
    echo ""
    print_header "🐳 Docker-based HuggingFace Token Validation"
    echo ""
    print_status "This container validates your HuggingFace token in the same environment"
    print_status "as your MedGemma deployment to ensure compatibility."
    echo ""
    print_status "Usage:"
    echo "  docker run --rm -e HUGGINGFACE_TOKEN=hf_your_token_here medgemma-validation"
    echo ""
    print_status "Environment Variables:"
    echo "  • HUGGINGFACE_TOKEN - Your HuggingFace token (required)"
    echo "  • HF_TOKEN - Alternative token variable name"
    echo ""
    print_status "Example:"
    echo "  docker build -f docker/Dockerfile.validation -t medgemma-validation ."
    echo "  docker run --rm -e HUGGINGFACE_TOKEN=hf_abc123... medgemma-validation"
    echo ""
}

# Main function
main() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              Docker Token Validation                        ║"
    print_header "║              MedGemma Environment Test                      ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi
    
    # Check environment setup
    if ! check_environment; then
        print_error "Environment check failed"
        exit 1
    fi
    
    echo ""
    
    # Check token environment variables
    if ! check_token_env; then
        print_error "Token environment check failed"
        show_usage
        exit 1
    fi
    
    echo ""
    
    # Run validation
    if run_validation; then
        echo ""
        print_success "🎉 Token validation successful in Docker environment!"
        print_status "Your token is ready for MedGemma deployment."
        exit 0
    else
        echo ""
        print_error "💥 Token validation failed in Docker environment."
        print_status "Please fix the issues before deploying MedGemma."
        exit 1
    fi
}

# Run main function
main "$@"
