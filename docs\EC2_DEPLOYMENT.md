# MedGemma EC2 Deployment Guide

This guide provides step-by-step instructions for deploying MedGemma FastAPI on AWS EC2 instances with proper HuggingFace authentication.

## 🚀 Quick Start

### Prerequisites
1. AWS account with EC2 access
2. HuggingFace account with MedGemma access
3. SSH key pair for EC2 access

### 1-Minute Deploy
```bash
# Launch EC2 instance (t3.xlarge or larger)
# SSH into instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Run setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/main/scripts/setup-ec2-instance.sh | bash

# Log out and back in for Docker group changes
exit
ssh -i your-key.pem ubuntu@your-ec2-ip

# Clone repository and deploy
cd ~/medgemma-deployment
git clone https://github.com/your-repo/docker-medgemma-fastapi.git .
./scripts/deploy-ec2.sh
```

## 📋 Detailed Setup

### Step 1: Launch EC2 Instance

#### Recommended Instance Types
| Instance Type | vCPU | RAM | Use Case |
|---------------|------|-----|----------|
| t3.xlarge | 4 | 16GB | Minimum viable |
| t3.2xlarge | 8 | 32GB | Recommended |
| m5.xlarge | 4 | 16GB | Balanced performance |
| c5.xlarge | 4 | 8GB | CPU optimized |
| r5.xlarge | 4 | 32GB | Memory optimized |

#### Launch Configuration
1. **AMI**: Ubuntu Server 20.04 LTS or Amazon Linux 2
2. **Instance Type**: t3.xlarge or larger
3. **Storage**: 30GB+ gp3 EBS volume
4. **Security Group**: Allow ports 22, 80, 443, 8000, 3000, 9091
5. **Key Pair**: Your SSH key for access

### Step 2: Configure Security Group

Create or modify security group with these inbound rules:

| Type | Protocol | Port Range | Source | Description |
|------|----------|------------|--------|-------------|
| SSH | TCP | 22 | Your IP | SSH access |
| HTTP | TCP | 80 | 0.0.0.0/0 | Web access |
| HTTPS | TCP | 443 | 0.0.0.0/0 | Secure web access |
| Custom TCP | TCP | 8000 | 0.0.0.0/0 | FastAPI |
| Custom TCP | TCP | 3000 | Your IP | Grafana (optional) |
| Custom TCP | TCP | 9091 | Your IP | Prometheus (optional) |

### Step 3: Setup HuggingFace Authentication

#### Get HuggingFace Token
1. Visit [HuggingFace Settings](https://huggingface.co/settings/tokens)
2. Create new token with "Read" permissions
3. Accept [MedGemma license](https://huggingface.co/google/medgemma-4b-it)

#### Store Token Locally (Recommended)

##### Option A: Environment File (.env) - Recommended
```bash
# Create .env file
cat > .env << EOF
# MedGemma Configuration
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here
API_KEY=your-secure-api-key
REDIS_PASSWORD=your-redis-password
ENVIRONMENT=production
LOG_LEVEL=INFO
EOF

# Secure the file (owner read/write only)
chmod 600 .env

# Ensure .env is not committed to version control
echo ".env" >> .gitignore
```

##### Option B: Direct Environment Variables
```bash
# Export in current session
export HUGGINGFACE_TOKEN="hf_your_token_here"
export HF_TOKEN="hf_your_token_here"

# Add to ~/.bashrc for persistence
echo 'export HUGGINGFACE_TOKEN="hf_your_token_here"' >> ~/.bashrc
source ~/.bashrc
```

##### Option C: Use Environment Template
```bash
# Copy from template and edit
cp .env.example .env
nano .env  # Edit and add your actual token
chmod 600 .env
```

### Step 4: Install Dependencies

Run the automated setup script:

```bash
# Download and run setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/main/scripts/setup-ec2-instance.sh -o setup-ec2.sh
chmod +x setup-ec2.sh
./setup-ec2.sh
```

Or install manually:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Log out and back in for group changes
exit
```

### Step 5: Deploy Application

```bash
# Clone repository
git clone https://github.com/your-repo/docker-medgemma-fastapi.git
cd docker-medgemma-fastapi

# Configure environment
cp .env.example .env
nano .env  # Add your HuggingFace token and other settings

# Secure the environment file
chmod 600 .env

# Run deployment script
./scripts/deploy-ec2.sh

# Or deploy manually
docker-compose -f docker-compose.ec2.yml up -d
```

## 🔧 Configuration Options

### Environment Variables

Create `.env` file with your configuration:

```bash
# Required Configuration
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here
API_KEY=your-secure-api-key

# Model Configuration
MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9

# Performance Configuration
WORKERS=1
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# Security Configuration
REDIS_PASSWORD=your-redis-password
GRAFANA_PASSWORD=your-grafana-password

# Environment Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false

# Domain Configuration (optional)
DOMAIN_NAME=your-domain.com
```

**Important**: Always secure your `.env` file:
```bash
# Set restrictive permissions
chmod 600 .env

# Ensure it's not committed to version control
echo ".env" >> .gitignore
```

### Docker Compose Profiles

Use different profiles for different deployment scenarios:

```bash
# Minimal deployment (API + Redis only)
docker-compose -f docker-compose.ec2.yml --profile minimal up -d

# Full deployment (API + Redis + Nginx + Monitoring)
docker-compose -f docker-compose.ec2.yml --profile full up -d

# Development deployment
docker-compose -f docker-compose.ec2.yml --profile dev up -d
```

## 📊 Monitoring and Maintenance

### Access URLs

After deployment, access these URLs (replace `your-ec2-ip`):

- **API**: http://your-ec2-ip:8000
- **API Docs**: http://your-ec2-ip:8000/docs
- **Health Check**: http://your-ec2-ip:8000/health
- **Grafana**: http://your-ec2-ip:3000 (admin/admin)
- **Prometheus**: http://your-ec2-ip:9091

### Monitoring Commands

```bash
# Check service status
docker-compose -f docker-compose.ec2.yml ps

# View logs
docker-compose -f docker-compose.ec2.yml logs -f medgemma-api

# Check resource usage
docker stats

# Monitor system resources
htop
df -h
free -h
```

### Maintenance Tasks

```bash
# Update application
git pull
docker-compose -f docker-compose.ec2.yml build --no-cache
docker-compose -f docker-compose.ec2.yml up -d

# Backup model cache
sudo tar -czf model-cache-backup-$(date +%Y%m%d).tar.gz /opt/medgemma/model_cache

# Clean up Docker
docker system prune -f
docker volume prune -f

# Restart services
docker-compose -f docker-compose.ec2.yml restart
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Model Download Fails
```bash
# Check if .env file exists and has token
if [ -f .env ]; then
    echo "✓ .env file exists"
    if grep -q "HUGGINGFACE_TOKEN=hf_" .env; then
        echo "✓ HuggingFace token found in .env"
    else
        echo "✗ No valid HuggingFace token in .env"
    fi
else
    echo "✗ .env file not found"
fi

# Check environment variables in container
docker-compose -f docker-compose.ec2.yml exec medgemma-api env | grep HF

# Test token validity
docker-compose -f docker-compose.ec2.yml exec medgemma-api python -c "
import os
from huggingface_hub import HfApi, login
token = os.getenv('HUGGINGFACE_TOKEN')
if token:
    try:
        login(token=token)
        api = HfApi(token=token)
        model_info = api.model_info('google/medgemma-4b-it')
        print('✓ Token is valid and has access to MedGemma')
    except Exception as e:
        print(f'✗ Token validation failed: {e}')
else:
    print('✗ No token found in environment')
"
```

#### 2. Out of Memory
```bash
# Check memory usage
free -h
docker stats

# Reduce workers or use smaller instance
# Edit docker-compose.ec2.yml:
# deploy:
#   resources:
#     limits:
#       memory: 8G
```

#### 3. Disk Space Issues
```bash
# Check disk usage
df -h

# Clean up Docker
docker system prune -a -f

# Move model cache to larger volume
sudo mv /opt/medgemma/model_cache /mnt/large-volume/
sudo ln -s /mnt/large-volume/model_cache /opt/medgemma/model_cache
```

#### 4. Network Issues
```bash
# Check security group
aws ec2 describe-security-groups --group-ids sg-your-group-id

# Test connectivity
curl -f http://localhost:8000/health
telnet your-ec2-ip 8000
```

### Log Analysis

```bash
# Application logs
docker-compose -f docker-compose.ec2.yml logs medgemma-api | grep ERROR

# System logs
sudo journalctl -u docker.service
sudo dmesg | tail

# Nginx logs (if using)
docker-compose -f docker-compose.ec2.yml logs nginx
```

## 🔒 Security Best Practices

1. **Secure .env files** with proper permissions (600)
2. **Never commit .env files** to version control
3. **Use strong API keys** and rotate them regularly
4. **Restrict security group** to necessary IPs only
5. **Use HTTPS** in production with SSL certificates
6. **Regular security updates** for OS and containers
7. **Monitor access logs** for suspicious activity
8. **Validate token format** (should start with `hf_`)
9. **Use unique passwords** for Redis and other services
10. **Backup .env files** securely and separately

## 💰 Cost Optimization

1. **Use Spot Instances** for development
2. **Schedule start/stop** for non-production environments
3. **Right-size instances** based on actual usage
4. **Use EBS gp3** volumes for better price/performance
5. **Enable detailed monitoring** only when needed
6. **Clean up unused resources** regularly

## 📚 Additional Resources

- [AWS EC2 User Guide](https://docs.aws.amazon.com/ec2/)
- [Docker on AWS](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/docker-basics.html)
- [HuggingFace Authentication](./HUGGINGFACE_AUTHENTICATION.md)
- [MedGemma Model Documentation](https://huggingface.co/google/medgemma-4b-it)
