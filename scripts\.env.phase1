# Phase 1: Standalone Core Model & API Configuration
# Complete environment configuration for Phase 1 deployment
# Copy this file to .env.phase1 and customize the values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API Key for authentication (REQUIRED - minimum 32 characters)
API_KEY=your-super-secret-api-key-32-chars-minimum-change-this

# Logging configuration
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=production

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Model settings
MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9
WORKERS=1

# =============================================================================
# HUGGINGFACE AUTHENTICATION (REQUIRED)
# =============================================================================

# HuggingFace token for model access (REQUIRED)
# Get your token from: https://huggingface.co/settings/tokens
# Make sure you have access to MedGemma-4b-it model
HUGGINGFACE_TOKEN=*************************************
HF_TOKEN=*************************************

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Server configuration
HOST=0.0.0.0
PORT=8000

# CORS and allowed hosts (permissive for Phase 1)
CORS_ORIGINS=*
ALLOWED_HOSTS=*

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# Maximum file upload size (10MB)
UPLOAD_MAX_SIZE=10485760

# =============================================================================
# CONVERSATION MANAGEMENT
# =============================================================================

# Conversation history settings
CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400  # 24 hours in seconds

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis password for conversation storage
REDIS_PASSWORD=your-secure-redis-password-change-this

# =============================================================================
# PHASE 1 SPECIFIC SETTINGS
# =============================================================================

# Phase 1 uses direct API access on port 8000
# No additional configuration needed for basic deployment

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================

# Uncomment and modify these if needed

# Custom model cache directory
# MODEL_CACHE_DIR=/app/model_cache

# Custom upload directory
# UPLOAD_DIR=/app/uploads

# Custom log directory
# LOG_DIR=/app/logs

# =============================================================================
# SECURITY NOTES FOR PHASE 1
# =============================================================================

# 1. Change the API_KEY to a strong, unique value
# 2. Change the REDIS_PASSWORD to a strong, unique value
# 3. Ensure your HuggingFace token is valid and has MedGemma access
# 4. Phase 1 uses HTTP only - consider upgrading to Phase 2+ for production

# =============================================================================
# DEPLOYMENT COMMANDS FOR PHASE 1
# =============================================================================

# After configuring this file:
# 1. Copy to .env.phase1: cp .env.phase1.example .env.phase1
# 2. Edit values: nano .env.phase1
# 3. Create data directories: ./scripts/setup-phase1-directories.sh
# 4. Deploy: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d
# 5. Monitor: docker-compose -f docker-compose.phase1.yml logs -f
# 6. Test: curl http://your-ec2-ip:8000/health
