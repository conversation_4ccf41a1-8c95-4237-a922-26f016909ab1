# Phase 4: Advanced Production Features
# Extends Phase 3 with enterprise monitoring, logging, and security
# Features: All Phase 1-3 + Prometheus, Grafana, advanced logging, backup automation

version: '3.8'

services:
  nginx:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx
    container_name: medgemma-nginx-phase4
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
      
    volumes:
      # Enhanced Nginx configuration with advanced security
      - ./nginx/nginx.phase4.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.phase4.conf:/etc/nginx/conf.d/default.conf:ro
      - ./frontend:/usr/share/nginx/html:ro
      - ssl_certs_phase4:/etc/nginx/ssl:ro
      - certbot_webroot_phase4:/var/www/certbot
      - nginx_logs_phase4:/var/log/nginx
      
    environment:
      - API_UPSTREAM=medgemma-api:8000
      - DOMAIN_NAME=${DOMAIN_NAME}
      - SSL_EMAIL=${SSL_EMAIL}
      - NGINX_CLIENT_MAX_BODY_SIZE=${NGINX_CLIENT_MAX_BODY_SIZE:-10m}
      - NGINX_WORKER_PROCESSES=${NGINX_WORKER_PROCESSES:-auto}
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=1m
      
    depends_on:
      medgemma-api:
        condition: service_healthy
        
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    networks:
      - medgemma-network-phase4

  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
      args:
        HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
        HF_TOKEN: ${HUGGINGFACE_TOKEN}
    container_name: medgemma-api-phase4
    restart: unless-stopped
    
    environment:
      # Model Configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      - WORKERS=${WORKERS:-1}
      
      # API Configuration
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # Network Configuration
      - HOST=0.0.0.0
      - PORT=8000
      - CORS_ORIGINS=${CORS_ORIGINS:-https://${DOMAIN_NAME},https://www.${DOMAIN_NAME}}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-${DOMAIN_NAME},www.${DOMAIN_NAME}}
      
      # File Upload Configuration
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      
      # Conversation Management
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - CONVERSATION_TTL=${CONVERSATION_TTL:-86400}
      
      # HuggingFace Authentication
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
      # Monitoring Configuration
      - PROMETHEUS_ENABLED=true
      - METRICS_ENABLED=true
      
    volumes:
      - model_cache_phase4:/app/model_cache
      - upload_data_phase4:/app/uploads
      - logs_phase4:/app/logs
      
    expose:
      - "8000"
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
      
    depends_on:
      redis:
        condition: service_healthy
        
    deploy:
      resources:
        limits:
          memory: 14G
        reservations:
          memory: 12G
          
    networks:
      - medgemma-network-phase4

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-phase4
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      - redis_data_phase4:/data
      
    expose:
      - "6379"
      
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
          
    networks:
      - medgemma-network-phase4

  prometheus:
    image: prom/prometheus:latest
    container_name: medgemma-prometheus-phase4
    restart: unless-stopped
    
    ports:
      - "9090:9090"
      
    volumes:
      - ./monitoring/prometheus.phase4.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_phase4:/prometheus
      
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION:-30d}'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      
    networks:
      - medgemma-network-phase4

  grafana:
    image: grafana/grafana:latest
    container_name: medgemma-grafana-phase4
    restart: unless-stopped
    
    ports:
      - "3000:3000"
      
    volumes:
      - grafana_data_phase4:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      
    depends_on:
      - prometheus
      
    networks:
      - medgemma-network-phase4

  certbot:
    image: certbot/certbot:latest
    container_name: medgemma-certbot-phase4
    restart: "no"
    
    volumes:
      - ssl_certs_phase4:/etc/letsencrypt
      - certbot_webroot_phase4:/var/www/certbot
      - certbot_logs_phase4:/var/log/letsencrypt
      
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
      - SSL_EMAIL=${SSL_EMAIL}
      - CERTBOT_STAGING=${CERTBOT_STAGING:-false}
      
    profiles:
      - certbot
      
    networks:
      - medgemma-network-phase4

  backup:
    build:
      context: .
      dockerfile: docker/Dockerfile.backup
    container_name: medgemma-backup-phase4
    restart: unless-stopped
    
    volumes:
      - backup_data_phase4:/backups
      - model_cache_phase4:/data/model_cache:ro
      - upload_data_phase4:/data/uploads:ro
      - redis_data_phase4:/data/redis:ro
      - ssl_certs_phase4:/data/ssl_certs:ro
      - logs_phase4:/data/logs:ro
      
    environment:
      - BACKUP_ENABLED=${BACKUP_ENABLED:-true}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
      - BACKUP_RETENTION_DAYS=7
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
    depends_on:
      - redis
      
    networks:
      - medgemma-network-phase4

volumes:
  # Phase 4 specific volumes
  model_cache_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/model_cache
      
  upload_data_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/uploads
      
  logs_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/logs
      
  nginx_logs_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/nginx_logs
      
  ssl_certs_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/ssl_certs
      
  certbot_webroot_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/certbot_webroot
      
  certbot_logs_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/certbot_logs
      
  redis_data_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/redis
      
  prometheus_data_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/prometheus
      
  grafana_data_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/grafana
      
  backup_data_phase4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase4/backups

networks:
  medgemma-network-phase4:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
