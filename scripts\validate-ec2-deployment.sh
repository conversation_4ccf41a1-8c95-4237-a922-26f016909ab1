#!/bin/bash
# Validation script for MedGemma EC2 deployment
# This script validates that the deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to check if running on EC2
check_ec2() {
    log "Checking EC2 environment..."
    
    if curl -s --connect-timeout 2 http://***************/latest/meta-data/instance-id >/dev/null 2>&1; then
        local instance_id=$(curl -s http://***************/latest/meta-data/instance-id)
        local instance_type=$(curl -s http://***************/latest/meta-data/instance-type)
        local public_ip=$(curl -s http://***************/latest/meta-data/public-ipv4)
        
        success "Running on EC2 instance: $instance_id ($instance_type)"
        log "Public IP: $public_ip"
        return 0
    else
        warn "Not running on EC2 or metadata service unavailable"
        return 1
    fi
}

# Function to check Docker installation
check_docker() {
    log "Checking Docker installation..."
    
    if command -v docker >/dev/null 2>&1; then
        local docker_version=$(docker --version)
        success "Docker installed: $docker_version"
        
        # Check if Docker daemon is running
        if docker ps >/dev/null 2>&1; then
            success "Docker daemon is running"
        else
            error "Docker daemon is not running"
            return 1
        fi
    else
        error "Docker is not installed"
        return 1
    fi
}

# Function to check Docker Compose
check_docker_compose() {
    log "Checking Docker Compose..."
    
    if command -v docker-compose >/dev/null 2>&1; then
        local compose_version=$(docker-compose --version)
        success "Docker Compose installed: $compose_version"
    else
        error "Docker Compose is not installed"
        return 1
    fi
}

# Function to check HuggingFace token
check_hf_token() {
    log "Checking HuggingFace token availability..."

    # Check environment variables
    if [ -n "$HUGGINGFACE_TOKEN" ] || [ -n "$HF_TOKEN" ]; then
        success "HuggingFace token found in environment variables"

        # Validate token format
        local token="${HUGGINGFACE_TOKEN:-$HF_TOKEN}"
        if [[ "$token" =~ ^hf_ ]]; then
            success "Token format appears valid (starts with hf_)"
        else
            warn "Token doesn't start with 'hf_' - please verify it's correct"
        fi
        return 0
    fi

    # Check .env file
    if [ -f ".env" ]; then
        if grep -q "HUGGINGFACE_TOKEN=" .env && ! grep -q "HUGGINGFACE_TOKEN=hf_your_token_here" .env; then
            success "HuggingFace token found in .env file"

            # Check file permissions
            local perms=$(stat -c "%a" .env)
            if [ "$perms" = "600" ]; then
                success ".env file has secure permissions (600)"
            else
                warn ".env file permissions are $perms (should be 600 for security)"
            fi
            return 0
        fi
    fi

    error "No HuggingFace token found"
    error "Please set HUGGINGFACE_TOKEN in .env file or environment variable"
    error "Example: HUGGINGFACE_TOKEN=hf_your_token_here"
    return 1
}

# Function to check system resources
check_resources() {
    log "Checking system resources..."
    
    # Check memory
    local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
    local mem_total_gb=$((mem_total / 1024 / 1024))
    local mem_available_gb=$((mem_available / 1024 / 1024))
    
    log "Total memory: ${mem_total_gb}GB"
    log "Available memory: ${mem_available_gb}GB"
    
    if [ $mem_available_gb -ge 8 ]; then
        success "Sufficient memory available"
    elif [ $mem_available_gb -ge 6 ]; then
        warn "Memory is adequate but may be tight (${mem_available_gb}GB)"
    else
        error "Insufficient memory (${mem_available_gb}GB). Recommend 8GB+"
    fi
    
    # Check disk space
    local disk_available=$(df / | tail -1 | awk '{print $4}')
    local disk_available_gb=$((disk_available / 1024 / 1024))
    
    log "Available disk space: ${disk_available_gb}GB"
    
    if [ $disk_available_gb -ge 20 ]; then
        success "Sufficient disk space available"
    else
        warn "Low disk space (${disk_available_gb}GB). Model download requires ~15-20GB"
    fi
}

# Function to check network connectivity
check_network() {
    log "Checking network connectivity..."
    
    # Check internet connectivity
    if curl -s --connect-timeout 5 https://google.com >/dev/null; then
        success "Internet connectivity: OK"
    else
        error "No internet connectivity"
        return 1
    fi
    
    # Check HuggingFace connectivity
    if curl -s --connect-timeout 5 https://huggingface.co >/dev/null; then
        success "HuggingFace connectivity: OK"
    else
        error "Cannot reach HuggingFace"
        return 1
    fi
    
    # Check Docker Hub connectivity
    if curl -s --connect-timeout 5 https://hub.docker.com >/dev/null; then
        success "Docker Hub connectivity: OK"
    else
        warn "Cannot reach Docker Hub (may affect image pulls)"
    fi
}

# Function to validate deployment files
check_deployment_files() {
    log "Checking deployment files..."
    
    local required_files=(
        "docker/Dockerfile.medgemma"
        "docker-compose.ec2.yml"
        "scripts/ec2-startup.sh"
        "scripts/download_model_runtime.py"
        "app/main.py"
        "requirements.txt"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            success "Found: $file"
        else
            error "Missing: $file"
            return 1
        fi
    done
}

# Function to test Docker build
test_docker_build() {
    log "Testing Docker build (dry run)..."
    
    # Check if Dockerfile is valid
    if docker build -f docker/Dockerfile.medgemma --dry-run . >/dev/null 2>&1; then
        success "Dockerfile syntax is valid"
    else
        error "Dockerfile has syntax errors"
        return 1
    fi
    
    # Check if docker-compose file is valid
    if docker-compose -f docker-compose.ec2.yml config >/dev/null 2>&1; then
        success "Docker Compose configuration is valid"
    else
        error "Docker Compose configuration has errors"
        return 1
    fi
}

# Function to check running services
check_running_services() {
    log "Checking running services..."
    
    if docker-compose -f docker-compose.ec2.yml ps | grep -q "Up"; then
        success "Services are running"
        
        # Check specific services
        local services=("medgemma-api-ec2" "medgemma-redis-ec2")
        for service in "${services[@]}"; do
            if docker ps | grep -q "$service"; then
                success "Service running: $service"
            else
                warn "Service not running: $service"
            fi
        done
    else
        warn "No services are currently running"
        log "To start services: docker-compose -f docker-compose.ec2.yml up -d"
    fi
}

# Function to test API endpoints
test_api_endpoints() {
    log "Testing API endpoints..."
    
    local base_url="http://localhost:8000"
    
    # Test health endpoint
    if curl -s -f "$base_url/health" >/dev/null; then
        success "Health endpoint is responding"
    else
        error "Health endpoint is not responding"
        return 1
    fi
    
    # Test docs endpoint
    if curl -s -f "$base_url/docs" >/dev/null; then
        success "API documentation is accessible"
    else
        warn "API documentation is not accessible"
    fi
    
    # Test metrics endpoint (if enabled)
    if curl -s -f "$base_url:9090/metrics" >/dev/null; then
        success "Metrics endpoint is responding"
    else
        warn "Metrics endpoint is not responding (may be disabled)"
    fi
}

# Function to generate validation report
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="validation-report-$(date +%Y%m%d-%H%M%S).txt"
    
    log "Generating validation report: $report_file"
    
    cat > "$report_file" << EOF
MedGemma EC2 Deployment Validation Report
Generated: $timestamp

=== System Information ===
$(uname -a)
$(lsb_release -a 2>/dev/null || cat /etc/os-release)

=== EC2 Instance Information ===
$(curl -s http://***************/latest/meta-data/instance-id 2>/dev/null || echo "Not on EC2")
$(curl -s http://***************/latest/meta-data/instance-type 2>/dev/null || echo "Not on EC2")
$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "No public IP")

=== Docker Information ===
$(docker --version 2>/dev/null || echo "Docker not installed")
$(docker-compose --version 2>/dev/null || echo "Docker Compose not installed")

=== System Resources ===
Memory: $(free -h | grep Mem)
Disk: $(df -h /)
CPU: $(nproc) cores

=== Running Containers ===
$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "No containers running")

=== Network Tests ===
Internet: $(curl -s --connect-timeout 5 https://google.com >/dev/null && echo "OK" || echo "FAIL")
HuggingFace: $(curl -s --connect-timeout 5 https://huggingface.co >/dev/null && echo "OK" || echo "FAIL")
Docker Hub: $(curl -s --connect-timeout 5 https://hub.docker.com >/dev/null && echo "OK" || echo "FAIL")

=== API Tests ===
Health: $(curl -s -f http://localhost:8000/health >/dev/null && echo "OK" || echo "FAIL")
Docs: $(curl -s -f http://localhost:8000/docs >/dev/null && echo "OK" || echo "FAIL")

=== Recommendations ===
EOF
    
    # Add recommendations based on findings
    local mem_gb=$(grep MemAvailable /proc/meminfo | awk '{print int($2/1024/1024)}')
    if [ $mem_gb -lt 8 ]; then
        echo "- Consider upgrading to an instance with more memory (current: ${mem_gb}GB, recommended: 8GB+)" >> "$report_file"
    fi
    
    local disk_gb=$(df / | tail -1 | awk '{print int($4/1024/1024)}')
    if [ $disk_gb -lt 20 ]; then
        echo "- Consider adding more disk space (current: ${disk_gb}GB available, recommended: 20GB+)" >> "$report_file"
    fi
    
    if ! docker ps | grep -q "medgemma-api"; then
        echo "- Start the MedGemma API service: docker-compose -f docker-compose.ec2.yml up -d" >> "$report_file"
    fi
    
    success "Validation report saved: $report_file"
}

# Main validation function
main() {
    echo "=== MedGemma EC2 Deployment Validation ==="
    echo "This script validates your EC2 deployment setup"
    echo
    
    local failed_checks=0
    
    # Run all checks
    check_ec2 || ((failed_checks++))
    check_docker || ((failed_checks++))
    check_docker_compose || ((failed_checks++))
    check_hf_token || ((failed_checks++))
    check_resources
    check_network || ((failed_checks++))
    check_deployment_files || ((failed_checks++))
    test_docker_build || ((failed_checks++))
    check_running_services
    test_api_endpoints
    
    # Generate report
    generate_report
    
    echo
    if [ $failed_checks -eq 0 ]; then
        success "All critical checks passed! ✅"
        echo "Your EC2 instance is ready for MedGemma deployment."
    else
        warn "$failed_checks critical checks failed ⚠️"
        echo "Please address the issues above before deploying."
    fi
    
    echo
    echo "Next steps:"
    echo "1. Review the validation report"
    echo "2. Fix any critical issues"
    echo "3. Deploy with: ./scripts/deploy-ec2.sh"
    echo "4. Monitor with: docker-compose -f docker-compose.ec2.yml logs -f"
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0"
    echo
    echo "This script validates your EC2 instance for MedGemma deployment."
    echo "It checks system requirements, Docker installation, network connectivity,"
    echo "and deployment file integrity."
    echo
    echo "The script generates a detailed validation report for troubleshooting."
    exit 0
fi

# Run main function
main "$@"
