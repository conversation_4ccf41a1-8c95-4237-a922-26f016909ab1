#!/bin/bash

# HuggingFace CLI Installation Script
# Installs the official HuggingFace CLI for token validation and model access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              HuggingFace CLI Installer                      ║"
    print_header "║                 Official CLI Setup                          ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if CLI is already installed
check_existing_installation() {
    print_status "Checking for existing HuggingFace CLI installation..."
    
    if command -v huggingface-cli &> /dev/null; then
        local version=$(huggingface-cli --version 2>/dev/null | head -1 || echo "Unknown version")
        print_success "HuggingFace CLI already installed: $version"
        
        echo -n "Do you want to upgrade to the latest version? (y/N): "
        read -r upgrade_choice
        if [[ $upgrade_choice =~ ^[Yy]$ ]]; then
            return 1  # Proceed with installation/upgrade
        else
            print_status "Keeping existing installation"
            return 0  # Skip installation
        fi
    else
        print_status "HuggingFace CLI not found - proceeding with installation"
        return 1  # Proceed with installation
    fi
}

# Function to check Python installation
check_python() {
    print_status "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version 2>&1)
        print_success "Python found: $python_version"
        
        # Check Python version (should be 3.7+)
        local version_number=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
        local major=$(echo $version_number | cut -d'.' -f1)
        local minor=$(echo $version_number | cut -d'.' -f2)
        
        if [[ $major -eq 3 && $minor -ge 7 ]] || [[ $major -gt 3 ]]; then
            print_success "Python version is compatible (3.7+ required)"
        else
            print_warning "Python version may be too old (3.7+ recommended)"
        fi
    else
        print_error "Python3 not found"
        print_status "Please install Python 3.7+ first:"
        print_status "  • Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
        print_status "  • CentOS/RHEL: sudo yum install python3 python3-pip"
        print_status "  • macOS: brew install python3"
        return 1
    fi
    
    # Check pip
    if command -v pip3 &> /dev/null || python3 -m pip --version &> /dev/null; then
        print_success "pip found"
    else
        print_error "pip not found"
        print_status "Install pip: sudo apt install python3-pip"
        return 1
    fi
    
    return 0
}

# Function to install HuggingFace CLI
install_hf_cli() {
    print_status "Installing HuggingFace CLI..."
    
    # Try different installation methods
    local install_success=false
    
    # Method 1: pip3 install
    if command -v pip3 &> /dev/null; then
        print_status "Attempting installation with pip3..."
        if pip3 install --upgrade huggingface_hub[cli] --user; then
            install_success=true
            print_success "Installation completed with pip3"
        else
            print_warning "pip3 installation failed, trying alternative method..."
        fi
    fi
    
    # Method 2: python3 -m pip install
    if [[ $install_success == false ]]; then
        print_status "Attempting installation with python3 -m pip..."
        if python3 -m pip install --upgrade huggingface_hub[cli] --user; then
            install_success=true
            print_success "Installation completed with python3 -m pip"
        else
            print_warning "python3 -m pip installation failed..."
        fi
    fi
    
    # Method 3: Try without [cli] extras
    if [[ $install_success == false ]]; then
        print_status "Attempting basic installation without CLI extras..."
        if python3 -m pip install --upgrade huggingface_hub --user; then
            install_success=true
            print_success "Basic installation completed"
            print_warning "CLI extras may not be available"
        fi
    fi
    
    if [[ $install_success == false ]]; then
        print_error "All installation methods failed"
        return 1
    fi
    
    return 0
}

# Function to verify installation
verify_installation() {
    print_status "Verifying HuggingFace CLI installation..."
    
    # Check if command is available
    if command -v huggingface-cli &> /dev/null; then
        local version=$(huggingface-cli --version 2>/dev/null | head -1 || echo "Unknown version")
        print_success "HuggingFace CLI is working: $version"
    else
        print_warning "huggingface-cli command not found in PATH"
        print_status "Checking if it's installed in user directory..."
        
        # Check common user installation paths
        local user_bin_paths=(
            "$HOME/.local/bin"
            "$HOME/Library/Python/*/bin"
            "$HOME/.pyenv/shims"
        )
        
        for path in "${user_bin_paths[@]}"; do
            if [[ -f "$path/huggingface-cli" ]]; then
                print_success "Found huggingface-cli in: $path"
                print_status "Add to PATH: export PATH=\"$path:\$PATH\""
                return 0
            fi
        done
        
        print_error "HuggingFace CLI not found after installation"
        return 1
    fi
    
    # Test basic functionality
    print_status "Testing CLI functionality..."
    if timeout 10 huggingface-cli --help > /dev/null 2>&1; then
        print_success "CLI help command works"
    else
        print_warning "CLI help command failed or timed out"
    fi
    
    return 0
}

# Function to setup PATH if needed
setup_path() {
    print_status "Checking PATH configuration..."
    
    # Common user bin directories
    local user_bin_dirs=(
        "$HOME/.local/bin"
        "$HOME/Library/Python/3.*/bin"
    )
    
    local path_updated=false
    
    for bin_dir in "${user_bin_dirs[@]}"; do
        # Expand wildcards
        for expanded_dir in $bin_dir; do
            if [[ -d "$expanded_dir" && ":$PATH:" != *":$expanded_dir:"* ]]; then
                print_status "Adding $expanded_dir to PATH"
                export PATH="$expanded_dir:$PATH"
                path_updated=true
            fi
        done
    done
    
    if [[ $path_updated == true ]]; then
        print_success "PATH updated for current session"
        print_status "To make permanent, add to your shell profile:"
        print_status "  echo 'export PATH=\"\$HOME/.local/bin:\$PATH\"' >> ~/.bashrc"
        print_status "  source ~/.bashrc"
    fi
}

# Function to show next steps
show_next_steps() {
    echo ""
    print_header "🎉 HuggingFace CLI Installation Complete!"
    echo ""
    
    print_status "Next steps:"
    echo "  1. Validate your token: ./scripts/validate-hf-token-cli.sh"
    echo "  2. Or login interactively: huggingface-cli login"
    echo "  3. Test authentication: huggingface-cli whoami"
    echo ""
    
    print_status "Common CLI commands:"
    echo "  • Login: huggingface-cli login"
    echo "  • Check auth: huggingface-cli whoami"
    echo "  • Download model: huggingface-cli download google/medgemma-4b-it config.json"
    echo "  • Logout: huggingface-cli logout"
    echo ""
    
    print_status "Documentation:"
    echo "  • CLI Guide: https://huggingface.co/docs/huggingface_hub/guides/cli"
    echo "  • Token Setup: https://huggingface.co/settings/tokens"
    echo ""
}

# Main function
main() {
    show_banner
    
    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0"
        echo ""
        echo "Installs the official HuggingFace CLI for token validation and model access"
        echo ""
        echo "This script will:"
        echo "  1. Check for existing installation"
        echo "  2. Verify Python and pip are available"
        echo "  3. Install huggingface_hub[cli] package"
        echo "  4. Verify the installation works"
        echo "  5. Setup PATH if needed"
        echo ""
        exit 0
    fi
    
    # Check if already installed
    if check_existing_installation; then
        print_success "HuggingFace CLI is already available"
        show_next_steps
        exit 0
    fi
    
    # Check Python installation
    if ! check_python; then
        print_error "Python installation check failed"
        exit 1
    fi
    
    # Install HuggingFace CLI
    if ! install_hf_cli; then
        print_error "HuggingFace CLI installation failed"
        print_status "Manual installation:"
        print_status "  pip3 install --upgrade huggingface_hub[cli] --user"
        exit 1
    fi
    
    # Setup PATH
    setup_path
    
    # Verify installation
    if ! verify_installation; then
        print_error "Installation verification failed"
        print_status "Try manual verification:"
        print_status "  huggingface-cli --version"
        print_status "  huggingface-cli --help"
        exit 1
    fi
    
    # Show next steps
    show_next_steps
    
    print_success "✅ HuggingFace CLI installation completed successfully!"
}

# Run main function
main "$@"
