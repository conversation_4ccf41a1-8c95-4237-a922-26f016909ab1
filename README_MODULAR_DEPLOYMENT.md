# MedGemma AI Chat - Modular Deployment Guide

## 🎯 Quick Start

**Deploy in 3 simple steps:**

```bash
# 1. Choose and deploy your phase
./scripts/deploy-phase.sh

# 2. Test your deployment
./scripts/test-all-phases.sh

# 3. Access your application
# Phase 1: http://your-ip:8000
# Phase 2: http://your-ip
# Phase 3+: https://your-domain
```

## 🏗️ Deployment Phases

### Phase 1: Standalone Core API ✅ **Fully Functional**
**Perfect for: Development, Testing, Quick Demos**

```bash
# Deploy Phase 1
./scripts/setup-phase1-directories.sh
cp .env.phase1.example .env.phase1
# Edit .env.phase1 with your settings
docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d
```

**Features:**
- ✅ Complete MedGemma-4b-it AI chat
- ✅ Text conversations with streaming
- ✅ Image upload and analysis
- ✅ Conversation memory management
- ✅ Basic HTML frontend
- ✅ Direct API access on port 8000

**Access:** `http://your-ec2-ip:8000`

---

### Phase 2: Production Nginx Setup ✅ **Fully Functional**
**Perfect for: Staging, Internal Production**

```bash
# Upgrade from Phase 1
./scripts/migrate-phase1-to-phase2.sh

# Or deploy fresh
./scripts/setup-phase2-directories.sh
cp .env.phase2.example .env.phase2
# Edit .env.phase2 with your settings
docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d
```

**Additional Features:**
- ✅ All Phase 1 features
- ✅ Professional Nginx reverse proxy
- ✅ Static file optimization
- ✅ Basic security headers
- ✅ Centralized logging

**Access:** `http://your-ec2-ip`

---

### Phase 3: SSL/TLS Security ✅ **Fully Functional**
**Perfect for: Public Production Deployment**

```bash
# Upgrade from Phase 2
./scripts/migrate-phase2-to-phase3.sh

# Setup SSL certificates
./scripts/setup-ssl-phase3.sh
```

**Additional Features:**
- ✅ All Phase 1 & 2 features
- ✅ SSL/TLS encryption with Let's Encrypt
- ✅ Automatic certificate renewal
- ✅ HTTPS redirect from HTTP
- ✅ Advanced security headers (HSTS, CSP)

**Access:** `https://your-domain.com`

---

### Phase 4: Enterprise Features ✅ **Fully Functional**
**Perfect for: Enterprise, Mission-Critical**

```bash
# Upgrade from Phase 3
./scripts/migrate-phase3-to-phase4.sh
```

**Additional Features:**
- ✅ All Phase 1, 2 & 3 features
- ✅ Prometheus monitoring
- ✅ Grafana dashboards
- ✅ Automated backup system
- ✅ Advanced security hardening
- ✅ Performance optimization

**Access:** 
- Main: `https://your-domain.com`
- Grafana: `https://your-domain.com:3000`
- Prometheus: `https://your-domain.com:9090`

## 🚀 Interactive Deployment

Use the interactive deployment assistant:

```bash
./scripts/deploy-phase.sh
```

This script will:
1. Show you all available phases
2. Check prerequisites
3. Help configure environment
4. Deploy your chosen phase
5. Test the deployment
6. Provide access information

## 📋 Prerequisites

### System Requirements
- **AWS EC2 t3.xlarge** (4 vCPU, 16 GB RAM)
- **Ubuntu 24.04 LTS**
- **50GB+ storage**
- **Docker & Docker Compose**

### Required Tokens
- **HuggingFace Token** with MedGemma-4b-it access
- **API Key** for authentication (you create this)

### For Phase 3+ (SSL)
- **Domain name** pointing to your EC2 instance
- **Email address** for SSL certificate registration

## 🔧 Management Commands

### Universal Commands (All Phases)
```bash
# Test any active deployment
./scripts/test-all-phases.sh

# View logs
docker-compose -f docker-compose.phase[N].yml logs -f

# Stop services
docker-compose -f docker-compose.phase[N].yml down

# Restart services
docker-compose -f docker-compose.phase[N].yml restart
```

### Phase-Specific Commands
```bash
# Phase 1
docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d

# Phase 2
docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d

# Phase 3
docker-compose -f docker-compose.phase3.yml --env-file .env.phase3 up -d

# Phase 4
docker-compose -f docker-compose.phase4.yml --env-file .env.phase4 up -d
```

## 🔄 Migration Between Phases

### Seamless Upgrades
```bash
# Phase 1 → Phase 2
./scripts/migrate-phase1-to-phase2.sh

# Phase 2 → Phase 3
./scripts/migrate-phase2-to-phase3.sh

# Phase 3 → Phase 4
./scripts/migrate-phase3-to-phase4.sh
```

### Data Preservation
All migrations preserve:
- ✅ Model cache (no re-download)
- ✅ Conversation history
- ✅ Uploaded images
- ✅ Configuration settings

## 📊 Testing & Validation

### Comprehensive Testing
```bash
# Test active deployment
./scripts/test-all-phases.sh

# Test specific phase
./scripts/test-all-phases.sh 2

# Phase-specific tests
./scripts/test-phase1.sh
./scripts/test-phase2.sh
./scripts/test-phase3.sh
./scripts/test-phase4.sh
```

### Manual Testing
```bash
# Health check
curl http://your-endpoint/health

# Chat test
curl -X POST http://your-endpoint/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"message": "Hello!"}'

# Image analysis test
curl -X POST http://your-endpoint/api/analyze-image \
  -H "Authorization: Bearer your-api-key" \
  -F "message=Analyze this image" \
  -F "image=@test-image.jpg"
```

## 🔒 Security Features

### Phase 1 Security
- API key authentication
- Basic input validation
- Local data storage

### Phase 2 Security
- Nginx security headers
- Rate limiting
- Request filtering

### Phase 3 Security
- SSL/TLS encryption
- HSTS headers
- Content Security Policy
- Automatic HTTPS redirect

### Phase 4 Security
- Advanced monitoring
- Intrusion detection
- Security alerting
- Automated security updates

## 📁 File Structure

```
├── docker-compose.phase1.yml    # Phase 1: Standalone API
├── docker-compose.phase2.yml    # Phase 2: + Nginx
├── docker-compose.phase3.yml    # Phase 3: + SSL
├── docker-compose.phase4.yml    # Phase 4: + Monitoring
├── .env.phase1.example          # Phase 1 config template
├── .env.phase2.example          # Phase 2 config template
├── .env.phase3.example          # Phase 3 config template
├── .env.phase4.example          # Phase 4 config template
├── scripts/
│   ├── deploy-phase.sh          # Interactive deployment
│   ├── test-all-phases.sh       # Universal testing
│   ├── setup-phase1-directories.sh
│   ├── migrate-phase1-to-phase2.sh
│   ├── setup-ssl-phase3.sh
│   └── ...
├── nginx/
│   ├── nginx.phase2.conf        # Phase 2 Nginx config
│   ├── nginx.phase3.conf        # Phase 3 Nginx config
│   └── conf.d/
└── data/
    ├── phase1/                  # Phase 1 data
    ├── phase2/                  # Phase 2 data
    ├── phase3/                  # Phase 3 data
    └── phase4/                  # Phase 4 data
```

## 🚨 Troubleshooting

### Common Issues

1. **Model Download Fails**
   ```bash
   # Check HuggingFace token
   docker-compose logs medgemma-api | grep -i token
   ```

2. **SSL Certificate Issues**
   ```bash
   # Regenerate certificates
   ./scripts/setup-ssl-phase3.sh
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   docker stats
   free -h
   ```

4. **Network Connectivity**
   ```bash
   # Check port accessibility
   netstat -tlnp | grep :8000
   ```

### Getting Help
- Check logs: `docker-compose logs -f`
- Run tests: `./scripts/test-all-phases.sh`
- Review configuration files
- Check [TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)

## 🎉 Success Indicators

### Phase 1 Success
- ✅ API accessible at `http://ip:8000`
- ✅ Health check returns "healthy"
- ✅ Chat endpoint responds
- ✅ Web interface loads

### Phase 2 Success
- ✅ All Phase 1 indicators
- ✅ Nginx proxy working
- ✅ API accessible at `http://ip/api/`
- ✅ Static files served by Nginx

### Phase 3 Success
- ✅ All Phase 2 indicators
- ✅ HTTPS access working
- ✅ HTTP redirects to HTTPS
- ✅ SSL certificate valid

### Phase 4 Success
- ✅ All Phase 3 indicators
- ✅ Prometheus collecting metrics
- ✅ Grafana dashboards accessible
- ✅ Backup system running

## 📞 Support

- **Documentation**: [docs/](docs/)
- **API Reference**: [docs/API.md](docs/API.md)
- **Configuration**: [docs/CONFIGURATION.md](docs/CONFIGURATION.md)
- **Troubleshooting**: [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)

---

**Choose the phase that meets your current needs - you can always upgrade later!**
