# Phase 4: Advanced Production Features Configuration
# Extends Phase 3 with enterprise monitoring, logging, and security
# Copy this file to .env.phase4 and customize the values

# =============================================================================
# API CONFIGURATION (from previous phases)
# =============================================================================

API_KEY=your-super-secret-api-key-32-chars-minimum-change-this
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=production

# =============================================================================
# MODEL CONFIGURATION (from previous phases)
# =============================================================================

MODEL_NAME=google/medgemma-4b-it
MAX_LENGTH=2048
TEMPERATURE=0.7
TOP_P=0.9
WORKERS=1

# =============================================================================
# HUGGINGFACE AUTHENTICATION (from previous phases)
# =============================================================================

HUGGINGFACE_TOKEN=hf_your_token_here_replace_with_actual_token
HF_TOKEN=hf_your_token_here_replace_with_actual_token

# =============================================================================
# DOMAIN CONFIGURATION (from Phase 3)
# =============================================================================

DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>
CERTBOT_STAGING=false

# =============================================================================
# NETWORK CONFIGURATION (from Phase 3)
# =============================================================================

CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# =============================================================================
# NGINX CONFIGURATION (Enhanced for Phase 4)
# =============================================================================

NGINX_CLIENT_MAX_BODY_SIZE=10m
NGINX_WORKER_PROCESSES=auto

# Advanced Nginx settings for Phase 4
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=1m

# =============================================================================
# MONITORING CONFIGURATION (New for Phase 4)
# =============================================================================

# Grafana admin password (REQUIRED)
GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password-change-this

# Prometheus retention period
PROMETHEUS_RETENTION=30d

# Enable metrics collection
PROMETHEUS_ENABLED=true
METRICS_ENABLED=true

# =============================================================================
# BACKUP CONFIGURATION (New for Phase 4)
# =============================================================================

# Enable automated backups
BACKUP_ENABLED=true

# Backup schedule (cron format: minute hour day month weekday)
# Default: 2 AM daily
BACKUP_SCHEDULE=0 2 * * *

# Backup retention (days)
BACKUP_RETENTION_DAYS=7

# =============================================================================
# FILE UPLOAD CONFIGURATION (from previous phases)
# =============================================================================

UPLOAD_MAX_SIZE=10485760

# =============================================================================
# CONVERSATION MANAGEMENT (from previous phases)
# =============================================================================

CONVERSATION_HISTORY_LIMIT=50
CONVERSATION_TTL=86400

# =============================================================================
# REDIS CONFIGURATION (from previous phases)
# =============================================================================

REDIS_PASSWORD=your-secure-redis-password-change-this

# =============================================================================
# PHASE 4 SPECIFIC SETTINGS
# =============================================================================

# Phase 4 adds enterprise features:
# - Prometheus monitoring on port 9090
# - Grafana dashboards on port 3000
# - Advanced logging and alerting
# - Automated backup system
# - Enhanced security and rate limiting
# - Performance optimization

# =============================================================================
# MIGRATION FROM PHASE 3
# =============================================================================

# To migrate from Phase 3 to Phase 4:
# 1. Stop Phase 3: docker-compose -f docker-compose.phase3.yml down
# 2. Copy data: ./scripts/migrate-phase3-to-phase4.sh
# 3. Update this config file
# 4. Deploy Phase 4: docker-compose -f docker-compose.phase4.yml --env-file .env.phase4 up -d

# =============================================================================
# DEPLOYMENT COMMANDS FOR PHASE 4
# =============================================================================

# After configuring this file:
# 1. Copy to .env.phase4: cp .env.phase4.example .env.phase4
# 2. Edit values: nano .env.phase4
# 3. Create data directories: ./scripts/setup-phase4-directories.sh
# 4. Deploy: docker-compose -f docker-compose.phase4.yml --env-file .env.phase4 up -d
# 5. Monitor: docker-compose -f docker-compose.phase4.yml logs -f
# 6. Test: curl https://your-domain.com/api/health
# 7. Access Grafana: https://your-domain.com:3000 (admin/your-grafana-password)
# 8. Access Prometheus: https://your-domain.com:9090

# =============================================================================
# PHASE 4 BENEFITS
# =============================================================================

# ✅ Prometheus monitoring and metrics
# ✅ Grafana dashboards and visualization
# ✅ Advanced logging and alerting
# ✅ Automated backup and recovery
# ✅ Enhanced security hardening
# ✅ Performance optimization
# ✅ Rate limiting and DDoS protection
# ✅ All previous phase functionality preserved

# =============================================================================
# MONITORING ENDPOINTS (Phase 4)
# =============================================================================

# Application monitoring:
# - Main app: https://your-domain.com
# - API health: https://your-domain.com/api/health
# - API metrics: https://your-domain.com/api/metrics
# - Prometheus: https://your-domain.com:9090
# - Grafana: https://your-domain.com:3000

# =============================================================================
# BACKUP CONFIGURATION (Phase 4)
# =============================================================================

# Automated backups include:
# - Model cache
# - Conversation data (Redis)
# - Uploaded images
# - SSL certificates
# - Application logs
# - Configuration files

# Backup location: ./data/phase4/backups/
# Restore command: ./scripts/restore-backup.sh backup-file.tar.gz

# =============================================================================
# SECURITY FEATURES (Phase 4)
# =============================================================================

# Enhanced security includes:
# - Rate limiting (configurable)
# - Advanced security headers
# - DDoS protection
# - Intrusion detection
# - Automated security updates
# - Security monitoring and alerting

# =============================================================================
# PERFORMANCE OPTIMIZATION (Phase 4)
# =============================================================================

# Performance features:
# - Optimized Nginx configuration
# - Connection pooling
# - Caching strategies
# - Resource monitoring
# - Automatic scaling preparation
# - Performance metrics and alerting

# =============================================================================
# TROUBLESHOOTING PHASE 4
# =============================================================================

# Common issues:
# 1. Grafana login: Use admin/your-grafana-password
# 2. Prometheus not collecting: Check service discovery
# 3. Backup failures: Check disk space and permissions
# 4. High resource usage: Monitor with Grafana dashboards

# =============================================================================
# ENTERPRISE READY
# =============================================================================

# Phase 4 provides enterprise-grade features:
# - 24/7 monitoring and alerting
# - Automated backup and disaster recovery
# - Security compliance and hardening
# - Performance optimization and scaling
# - Comprehensive logging and audit trails
