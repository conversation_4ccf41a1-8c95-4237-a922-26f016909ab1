#!/bin/bash

# Phase 1 Directory Setup Script
# Creates all necessary directories for Phase 1 deployment

set -e

echo "🚀 Setting up Phase 1 directories..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_status "Project root: $PROJECT_ROOT"

# Create Phase 1 data directories
PHASE1_DATA_DIR="$PROJECT_ROOT/data/phase1"

print_status "Creating Phase 1 data directories..."

# Create main data directory
mkdir -p "$PHASE1_DATA_DIR"

# Create subdirectories
directories=(
    "model_cache"
    "uploads"
    "logs"
    "redis"
)

for dir in "${directories[@]}"; do
    mkdir -p "$PHASE1_DATA_DIR/$dir"
    print_status "Created directory: $PHASE1_DATA_DIR/$dir"
done

# Set appropriate permissions
print_status "Setting directory permissions..."

# Make directories writable by Docker containers
chmod -R 755 "$PHASE1_DATA_DIR"

# Create .gitkeep files to preserve directory structure
for dir in "${directories[@]}"; do
    touch "$PHASE1_DATA_DIR/$dir/.gitkeep"
done

print_success "Phase 1 directories created successfully!"

# Check if .env.phase1 exists
if [[ ! -f "$PROJECT_ROOT/.env.phase1" ]]; then
    print_warning ".env.phase1 file not found"
    print_status "Creating .env.phase1 from template..."
    
    if [[ -f "$PROJECT_ROOT/.env.phase1.example" ]]; then
        cp "$PROJECT_ROOT/.env.phase1.example" "$PROJECT_ROOT/.env.phase1"
        print_success "Created .env.phase1 from template"
        print_warning "Please edit .env.phase1 with your configuration:"
        print_warning "  - Set your API_KEY"
        print_warning "  - Set your HUGGINGFACE_TOKEN"
        print_warning "  - Set your REDIS_PASSWORD"
    else
        print_error ".env.phase1.example template not found"
        exit 1
    fi
fi

# Display directory structure
print_status "Phase 1 directory structure:"
tree "$PHASE1_DATA_DIR" 2>/dev/null || find "$PHASE1_DATA_DIR" -type d | sed 's|[^/]*/|  |g'

# Display next steps
echo ""
print_success "Phase 1 setup complete!"
echo ""
print_status "Next steps:"
echo "  1. Edit your configuration: nano .env.phase1"
echo "  2. Validate HuggingFace token: ./scripts/validate-hf-token.sh"
echo "  3. Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d"
echo "  4. Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f"
echo "  5. Test deployment: ./scripts/test-phase1.sh"
echo ""
print_status "Phase 1 will be accessible at: http://your-ec2-ip:8000"
echo ""
print_warning "⚠️  IMPORTANT: Validate your HuggingFace token before deployment!"
print_status "Run: ./scripts/validate-hf-token.sh"

# Check Docker and Docker Compose
print_status "Checking Docker installation..."

if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    print_success "Docker found: $DOCKER_VERSION"
else
    print_error "Docker not found. Please install Docker first."
    exit 1
fi

if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_VERSION=$(docker-compose --version)
    print_success "Docker Compose found: $DOCKER_COMPOSE_VERSION"
else
    print_error "Docker Compose not found. Please install Docker Compose first."
    exit 1
fi

# Check available disk space
print_status "Checking available disk space..."
AVAILABLE_SPACE=$(df -h "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
print_status "Available space: $AVAILABLE_SPACE"

# Warn if less than 20GB available
AVAILABLE_GB=$(df -BG "$PROJECT_ROOT" | awk 'NR==2 {print $4}' | sed 's/G//')
if [[ $AVAILABLE_GB -lt 20 ]]; then
    print_warning "Less than 20GB available. MedGemma model requires ~8GB, consider freeing up space."
fi

print_success "Phase 1 setup verification complete!"
