#!/bin/bash

# Docker-based HuggingFace Token Validation Script
# Runs validation in the same environment as MedGemma deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${PURPLE}[STEP $1]${NC} $2"
}

# Configuration
VALIDATION_IMAGE="medgemma-validation"
VALIDATION_CONTAINER="medgemma-token-validation"

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              Docker Token Validation                        ║"
    print_header "║              Same Environment as Deployment                 ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_step "1" "Checking prerequisites..."
    
    local prereq_issues=0
    
    # Check Docker
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker found: $docker_version"
    else
        print_error "Docker not found"
        print_status "Install Docker: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
        ((prereq_issues++))
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker Compose found: $compose_version"
    else
        print_error "Docker Compose not found"
        ((prereq_issues++))
    fi
    
    # Check if Docker daemon is running
    if docker info &> /dev/null; then
        print_success "Docker daemon is running"
    else
        print_error "Docker daemon is not running"
        print_status "Start Docker service: sudo systemctl start docker"
        ((prereq_issues++))
    fi
    
    echo ""
    if [[ $prereq_issues -eq 0 ]]; then
        print_success "Prerequisites check passed"
        return 0
    else
        print_error "$prereq_issues prerequisite issues found"
        return 1
    fi
}

# Function to get token from environment
get_token_from_env() {
    print_step "2" "Checking token configuration..."
    
    local token=""
    local token_source=""
    
    # Check command line argument first
    if [[ $# -gt 0 && -n "$1" ]]; then
        token="$1"
        token_source="command line argument"
    # Check environment variable
    elif [[ -n "$HUGGINGFACE_TOKEN" ]]; then
        token="$HUGGINGFACE_TOKEN"
        token_source="HUGGINGFACE_TOKEN environment variable"
    # Check .env.phase1 file
    elif [[ -f ".env.phase1" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env.phase1 | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            token_source=".env.phase1 file"
        fi
    # Check .env file
    elif [[ -f ".env" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            token_source=".env file"
        fi
    # Check HF_TOKEN as fallback
    elif [[ -n "$HF_TOKEN" ]]; then
        token="$HF_TOKEN"
        token_source="HF_TOKEN environment variable"
    fi
    
    if [[ -z "$token" ]]; then
        print_error "No HuggingFace token found!"
        print_status "Please provide a token using one of these methods:"
        print_status "1. Command line: $0 hf_your_token_here"
        print_status "2. Environment: export HUGGINGFACE_TOKEN=hf_your_token_here"
        print_status "3. .env.phase1 file: HUGGINGFACE_TOKEN=hf_your_token_here"
        return 1
    fi
    
    print_success "Token found from $token_source"
    
    # Export for Docker
    export HUGGINGFACE_TOKEN="$token"
    export HF_TOKEN="$token"
    
    echo ""
    return 0
}

# Function to build validation image
build_validation_image() {
    print_step "3" "Building validation Docker image..."
    
    # Check if Dockerfile exists
    if [[ ! -f "docker/Dockerfile.validation" ]]; then
        print_error "Validation Dockerfile not found: docker/Dockerfile.validation"
        return 1
    fi
    
    # Build the image
    print_status "Building $VALIDATION_IMAGE image..."
    if docker build -f docker/Dockerfile.validation -t "$VALIDATION_IMAGE" . > /tmp/docker_build.log 2>&1; then
        print_success "Validation image built successfully"
    else
        print_error "Failed to build validation image"
        print_status "Build log:"
        cat /tmp/docker_build.log | tail -20
        return 1
    fi
    
    echo ""
    return 0
}

# Function to run validation container
run_validation_container() {
    print_step "4" "Running validation in Docker container..."
    
    # Clean up any existing container
    if docker ps -a | grep -q "$VALIDATION_CONTAINER"; then
        print_status "Removing existing validation container..."
        docker rm -f "$VALIDATION_CONTAINER" > /dev/null 2>&1 || true
    fi
    
    # Create validation data directory
    mkdir -p data/validation
    
    # Run the validation container
    print_status "Starting validation container..."
    
    local exit_code
    docker run --rm \
        --name "$VALIDATION_CONTAINER" \
        -e HUGGINGFACE_TOKEN="$HUGGINGFACE_TOKEN" \
        -e HF_TOKEN="$HF_TOKEN" \
        -v "$(pwd)/data/validation:/app/validation/logs" \
        "$VALIDATION_IMAGE"
    
    exit_code=$?
    
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        print_success "Docker validation completed successfully!"
        return 0
    else
        print_error "Docker validation failed!"
        return 1
    fi
}

# Function to run validation using Docker Compose
run_validation_compose() {
    print_step "4" "Running validation with Docker Compose..."
    
    # Check if compose file exists
    if [[ ! -f "docker-compose.validation.yml" ]]; then
        print_error "Validation compose file not found: docker-compose.validation.yml"
        return 1
    fi
    
    # Create validation data directory
    mkdir -p data/validation
    
    # Run validation with Docker Compose
    print_status "Starting validation with Docker Compose..."
    
    if docker-compose -f docker-compose.validation.yml run --rm hf-token-validation; then
        print_success "Docker Compose validation completed successfully!"
        return 0
    else
        print_error "Docker Compose validation failed!"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "Usage: $0 [options] [token]"
    echo ""
    echo "Docker-based HuggingFace token validation for MedGemma deployment"
    echo ""
    echo "Options:"
    echo "  --compose, -c    Use Docker Compose for validation"
    echo "  --help, -h       Show this help message"
    echo ""
    echo "Arguments:"
    echo "  token            Optional HuggingFace token (if not in environment)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Auto-detect token"
    echo "  $0 hf_your_token_here                # Use specific token"
    echo "  $0 --compose                         # Use Docker Compose"
    echo ""
    echo "Environment Variables:"
    echo "  HUGGINGFACE_TOKEN    Your HuggingFace token"
    echo "  HF_TOKEN             Alternative token variable"
    echo ""
    echo "Files Checked (in order):"
    echo "  1. Command line argument"
    echo "  2. HUGGINGFACE_TOKEN environment variable"
    echo "  3. .env.phase1 file"
    echo "  4. .env file"
    echo "  5. HF_TOKEN environment variable"
    echo ""
}

# Function to show results summary
show_results_summary() {
    echo ""
    print_header "📊 Docker Validation Summary"
    echo ""
    
    print_status "✅ Validation completed in Docker environment"
    print_status "🐳 Same environment as MedGemma deployment"
    print_status "🔒 Token permissions verified"
    print_status "📁 Model access confirmed"
    print_status "⬇️  Download capability tested"
    echo ""
    
    print_success "🎉 Your token is ready for MedGemma deployment!"
    echo ""
    
    print_status "Next steps:"
    echo "  1. Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d"
    echo "  2. Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f"
    echo "  3. Test deployment: ./scripts/test-phase1.sh"
    echo ""
    
    print_status "The validation used the exact same Docker environment as your deployment,"
    print_status "so you can be confident that the token will work in production!"
}

# Main function
main() {
    show_banner
    
    local use_compose=false
    local token_arg=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --compose|-c)
                use_compose=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            hf_*)
                token_arg="$1"
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Get token from environment
    if ! get_token_from_env "$token_arg"; then
        exit 1
    fi
    
    # Build validation image (unless using compose)
    if [[ $use_compose == false ]]; then
        if ! build_validation_image; then
            exit 1
        fi
        
        # Run validation container
        if ! run_validation_container; then
            exit 1
        fi
    else
        # Run validation with Docker Compose
        if ! run_validation_compose; then
            exit 1
        fi
    fi
    
    # Show results summary
    show_results_summary
}

# Run main function
main "$@"
