#!/bin/bash

# Phase 2 Directory Setup Script
# Creates all necessary directories for Phase 2 deployment

set -e

echo "🚀 Setting up Phase 2 directories..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_status "Project root: $PROJECT_ROOT"

# Create Phase 2 data directories
PHASE2_DATA_DIR="$PROJECT_ROOT/data/phase2"

print_status "Creating Phase 2 data directories..."

# Create main data directory
mkdir -p "$PHASE2_DATA_DIR"

# Create subdirectories
directories=(
    "model_cache"
    "uploads"
    "logs"
    "nginx_logs"
    "redis"
)

for dir in "${directories[@]}"; do
    mkdir -p "$PHASE2_DATA_DIR/$dir"
    print_status "Created directory: $PHASE2_DATA_DIR/$dir"
done

# Set appropriate permissions
print_status "Setting directory permissions..."

# Make directories writable by Docker containers
chmod -R 755 "$PHASE2_DATA_DIR"

# Create .gitkeep files to preserve directory structure
for dir in "${directories[@]}"; do
    touch "$PHASE2_DATA_DIR/$dir/.gitkeep"
done

print_success "Phase 2 directories created successfully!"

# Check if .env.phase2 exists
if [[ ! -f "$PROJECT_ROOT/.env.phase2" ]]; then
    print_warning ".env.phase2 file not found"
    print_status "Creating .env.phase2 from template..."
    
    if [[ -f "$PROJECT_ROOT/.env.phase2.example" ]]; then
        cp "$PROJECT_ROOT/.env.phase2.example" "$PROJECT_ROOT/.env.phase2"
        print_success "Created .env.phase2 from template"
        print_warning "Please edit .env.phase2 with your configuration:"
        print_warning "  - Set your API_KEY"
        print_warning "  - Set your HUGGINGFACE_TOKEN"
        print_warning "  - Set your REDIS_PASSWORD"
        print_warning "  - Set your DOMAIN_NAME"
    else
        print_error ".env.phase2.example template not found"
        exit 1
    fi
fi

# Display directory structure
print_status "Phase 2 directory structure:"
tree "$PHASE2_DATA_DIR" 2>/dev/null || find "$PHASE2_DATA_DIR" -type d | sed 's|[^/]*/|  |g'

# Display next steps
echo ""
print_success "Phase 2 setup complete!"
echo ""
print_status "Next steps:"
echo "  1. Edit your configuration: nano .env.phase2"
echo "  2. Deploy Phase 2: docker-compose -f docker-compose.phase2.yml --env-file .env.phase2 up -d"
echo "  3. Monitor deployment: docker-compose -f docker-compose.phase2.yml logs -f"
echo "  4. Test deployment: ./scripts/test-all-phases.sh 2"
echo ""
print_status "Phase 2 will be accessible at: http://your-ec2-ip"

print_success "Phase 2 setup verification complete!"
