#!/usr/bin/env python3

"""
Docker-based HuggingFace Token Validation
Runs inside the same environment as the MedGemma deployment
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_status(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def print_header(message: str):
    print(f"{Colors.CYAN}{message}{Colors.NC}")

def print_step(step: int, message: str):
    print(f"{Colors.PURPLE}[STEP {step}]{Colors.NC} {message}")

class DockerHFValidator:
    def __init__(self, model_name: str = "google/medgemma-4b-it"):
        self.model_name = model_name
        self.temp_cache_dir = None
        
    def setup_temp_cache(self):
        """Create a temporary cache directory with proper permissions"""
        print_status("Setting up temporary cache directory...")
        
        # Create a temporary directory that the user can write to
        self.temp_cache_dir = tempfile.mkdtemp(prefix="hf_validation_")
        
        # Set environment variables to use temp cache
        os.environ['HF_HOME'] = self.temp_cache_dir
        os.environ['HF_HUB_CACHE'] = self.temp_cache_dir
        os.environ['TRANSFORMERS_CACHE'] = self.temp_cache_dir
        
        print_success(f"Temporary cache directory: {self.temp_cache_dir}")
        return True
        
    def cleanup_temp_cache(self):
        """Clean up temporary cache directory"""
        if self.temp_cache_dir and os.path.exists(self.temp_cache_dir):
            try:
                shutil.rmtree(self.temp_cache_dir)
                print_status("Cleaned up temporary cache directory")
            except Exception as e:
                print_warning(f"Could not clean up temp directory: {e}")
    
    def get_token_from_env(self) -> Optional[str]:
        """Get HuggingFace token from environment variables"""
        
        # Check various environment variable names
        token_vars = ['HUGGINGFACE_TOKEN', 'HF_TOKEN']
        
        for var_name in token_vars:
            token = os.getenv(var_name)
            if token and token != "hf_your_token_here_replace_with_actual_token":
                print_status(f"Using token from {var_name} environment variable")
                return token
        
        return None
    
    def validate_token_format(self, token: str) -> bool:
        """Validate HuggingFace token format"""
        print_step(1, "Validating token format...")
        
        if not token:
            print_error("Token is empty")
            return False
            
        if not token.startswith('hf_'):
            print_error("Token must start with 'hf_'")
            print_status(f"Your token starts with: {token[:10]}...")
            return False
            
        if len(token) != 37:
            print_error(f"Token must be exactly 37 characters (got {len(token)})")
            print_status("Expected format: hf_ + 34 alphanumeric characters")
            return False
            
        # Check if it contains only valid characters
        valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_')
        if not all(c in valid_chars for c in token):
            print_error("Token contains invalid characters")
            return False
            
        print_success("Token format is valid")
        return True
    
    def test_authentication(self, token: str) -> Dict[str, Any]:
        """Test HuggingFace authentication without login"""
        print_step(2, "Testing HuggingFace authentication...")
        
        try:
            from huggingface_hub import HfApi
            
            # Create API instance with token (no login required)
            api = HfApi(token=token)
            
            # Test authentication by getting user info
            user_info = api.whoami()
            
            print_success("Authentication successful")
            print_status(f"Authenticated as: {user_info.get('name', 'Unknown')}")
            
            if 'email' in user_info:
                print_status(f"Email: {user_info['email']}")
            
            if 'orgs' in user_info and user_info['orgs']:
                print_status(f"Member of {len(user_info['orgs'])} organizations")
            
            return {'success': True, 'user_info': user_info}
            
        except Exception as e:
            print_error(f"Authentication failed: {e}")
            
            error_str = str(e).lower()
            if 'unauthorized' in error_str or '401' in error_str:
                print_status("Token is invalid or expired")
                print_status("Generate a new token at: https://huggingface.co/settings/tokens")
            elif 'forbidden' in error_str or '403' in error_str:
                print_status("Token has insufficient permissions")
            elif 'network' in error_str or 'connection' in error_str:
                print_status("Network connectivity issue")
            
            return {'success': False, 'error': str(e)}
    
    def test_model_access(self, token: str) -> Dict[str, Any]:
        """Test access to the specific model"""
        print_step(3, f"Testing access to {self.model_name}...")
        
        try:
            from huggingface_hub import HfApi
            
            api = HfApi(token=token)
            
            # Get model information
            model_info = api.model_info(self.model_name)
            
            print_success("Model access successful")
            
            # Display model information
            print_status("Model information:")
            print_status(f"  Model ID: {model_info.id}")
            if hasattr(model_info, 'downloads'):
                print_status(f"  Downloads: {model_info.downloads:,}")
            if hasattr(model_info, 'likes'):
                print_status(f"  Likes: {model_info.likes:,}")
            if hasattr(model_info, 'private'):
                print_status(f"  Private: {model_info.private}")
            if hasattr(model_info, 'gated'):
                print_status(f"  Gated: {model_info.gated}")
                if model_info.gated:
                    print_warning("This is a gated model - license acceptance required")
            
            return {'success': True, 'model_info': model_info}
            
        except Exception as e:
            print_error(f"Model access failed: {e}")
            
            error_str = str(e).lower()
            if 'not found' in error_str or '404' in error_str:
                print_status(f"Model not found: {self.model_name}")
            elif 'access denied' in error_str or 'forbidden' in error_str or '403' in error_str:
                print_status("Access denied - you may need to:")
                print_status("  1. Accept the model's license agreement")
                print_status("  2. Request access to the gated model")
                print_status("  3. Ensure your token has proper permissions")
                print_status(f"  4. Visit: https://huggingface.co/{self.model_name}")
            elif 'unauthorized' in error_str or '401' in error_str:
                print_status("Authentication failed - check your token")
            
            return {'success': False, 'error': str(e)}
    
    def test_model_files_access(self, token: str) -> Dict[str, Any]:
        """Test access to model files"""
        print_step(4, "Testing model file access...")
        
        try:
            from huggingface_hub import HfApi
            
            api = HfApi(token=token)
            
            # List model files
            files = list(api.list_repo_files(self.model_name, repo_type="model"))
            
            print_success("Model files accessible")
            print_status(f"Model contains {len(files)} files")
            
            # Show some key files
            key_files = ['config.json', 'pytorch_model.bin', 'tokenizer.json', 'model.safetensors']
            found_files = [f for f in files if any(key in f for key in key_files)]
            
            if found_files:
                print_status(f"  Key files found: {', '.join(found_files[:5])}")
            
            return {'success': True, 'files': files}
            
        except Exception as e:
            print_error(f"Model files access failed: {e}")
            
            error_str = str(e).lower()
            if 'access denied' in error_str or 'forbidden' in error_str or '403' in error_str:
                print_status("Files access denied")
                print_status("You can see the model but cannot access files")
                print_status("This indicates the license agreement hasn't been accepted")
                print_status(f"Visit: https://huggingface.co/{self.model_name}")
            
            return {'success': False, 'error': str(e)}
    
    def test_download_capability(self, token: str) -> Dict[str, Any]:
        """Test actual download capability"""
        print_step(5, "Testing download capability...")
        
        try:
            from huggingface_hub import hf_hub_download
            
            # Download config.json to temp directory
            print_status("Downloading config.json for validation...")
            
            config_path = hf_hub_download(
                repo_id=self.model_name,
                filename="config.json",
                token=token,
                cache_dir=self.temp_cache_dir,
                local_files_only=False
            )
            
            print_success("Download test successful")
            print_status(f"Downloaded to: {config_path}")
            
            # Try to parse the config
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                print_status("Successfully parsed config.json")
                
                # Show some config info
                if 'model_type' in config:
                    print_status(f"  Model type: {config['model_type']}")
                if 'vocab_size' in config:
                    print_status(f"  Vocabulary size: {config['vocab_size']:,}")
                if 'hidden_size' in config:
                    print_status(f"  Hidden size: {config['hidden_size']}")
                
                return {'success': True, 'config': config, 'path': config_path}
                
            except json.JSONDecodeError as e:
                print_warning(f"Downloaded file but JSON parsing failed: {e}")
                return {'success': False, 'error': f'Invalid JSON: {e}'}
                
        except Exception as e:
            print_error(f"Download failed: {e}")
            
            error_str = str(e).lower()
            if 'access denied' in error_str or 'forbidden' in error_str or '403' in error_str:
                print_status("Download access denied")
                print_status("You may need to accept the license agreement at:")
                print_status(f"https://huggingface.co/{self.model_name}")
            elif 'not found' in error_str or '404' in error_str:
                print_status("File not found - model may not have config.json")
            elif 'network' in error_str or 'timeout' in error_str:
                print_status("Download failed due to network issues")
            
            return {'success': False, 'error': str(e)}
    
    def validate_token(self, token: str) -> bool:
        """Run complete token validation in Docker environment"""
        print_header("🧪 Running Docker-based HuggingFace Token Validation")
        print_status(f"Validating access to: {self.model_name}")
        print_status(f"Cache directory: {self.temp_cache_dir}")
        print("")
        
        tests = [
            self.validate_token_format,
            self.test_authentication,
            self.test_model_access,
            self.test_model_files_access,
            self.test_download_capability
        ]
        
        passed = 0
        total = len(tests)
        
        for i, test in enumerate(tests):
            try:
                if i == 0:  # Format validation returns bool
                    result = test(token)
                    if result:
                        passed += 1
                    else:
                        break
                else:  # Other tests return dict
                    result = test(token)
                    if result.get('success'):
                        passed += 1
                    else:
                        break
                        
                print("")  # Add spacing between tests
                
            except Exception as e:
                print_error(f"Test {i+1} failed with exception: {e}")
                break
        
        # Summary
        print_header("📊 Docker Validation Summary")
        print_success(f"Passed: {passed}/{total} tests")
        
        if passed == total:
            print_success("✅ All tests passed! Your token is ready for deployment.")
            print("")
            print_status("🚀 The token works in the Docker environment!")
            print_status("You can now deploy with confidence:")
            print_status("  docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d")
            return True
        else:
            print_error("❌ Some tests failed in the Docker environment.")
            print_status("Please fix the issues before deployment.")
            return False

def show_help():
    """Show help instructions"""
    print_header("📋 Docker-based HuggingFace Token Validation")
    print("")
    print_status("This script validates your HuggingFace token inside a Docker container")
    print_status("using the same environment as your MedGemma deployment.")
    print("")
    print_status("Environment Variables Required:")
    print("  • HUGGINGFACE_TOKEN or HF_TOKEN - Your HuggingFace token")
    print("")
    print_status("How to Fix Issues:")
    print("1. Get a HuggingFace Token:")
    print("   • Go to: https://huggingface.co/settings/tokens")
    print("   • Click 'New token'")
    print("   • Choose 'Read' permissions")
    print("")
    print("2. Accept MedGemma License:")
    print("   • Go to: https://huggingface.co/google/medgemma-4b-it")
    print("   • Read and accept the license agreement")
    print("")

def main():
    # Show banner
    print("")
    print_header("╔══════════════════════════════════════════════════════════════╗")
    print_header("║              Docker HuggingFace Validator                   ║")
    print_header("║                 MedGemma-4b-it Access                       ║")
    print_header("║              (Same Environment as Deployment)               ║")
    print_header("╚══════════════════════════════════════════════════════════════╝")
    print("")
    
    validator = DockerHFValidator()
    
    try:
        # Setup temporary cache directory
        if not validator.setup_temp_cache():
            print_error("Failed to setup temporary cache directory")
            sys.exit(1)
        
        # Get token from environment
        token = validator.get_token_from_env()
        
        if not token:
            print_error("No HuggingFace token found!")
            print_status("Please set HUGGINGFACE_TOKEN environment variable")
            print_status("Example: docker run -e HUGGINGFACE_TOKEN=hf_your_token_here ...")
            print("")
            show_help()
            sys.exit(1)
        
        # Validate token
        success = validator.validate_token(token)
        
        if not success:
            print("")
            show_help()
            sys.exit(1)
            
    finally:
        # Always cleanup
        validator.cleanup_temp_cache()

if __name__ == "__main__":
    main()
