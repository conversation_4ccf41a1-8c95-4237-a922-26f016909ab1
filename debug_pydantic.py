#!/usr/bin/env python3

"""
Debug script to test the Pydantic configuration fix locally
"""

import os
import sys

def test_pydantic_fix():
    """Test the Pydantic fix with different configurations"""
    
    print("Testing Pydantic CORS_ORIGINS fix...")
    print("=" * 50)
    
    # Set up environment
    os.environ['CORS_ORIGINS'] = '*'
    os.environ['ALLOWED_HOSTS'] = '*'
    os.environ['API_KEY'] = 'test-api-key-32-characters-long'
    os.environ['HUGGINGFACE_TOKEN'] = 'hf_test_token'
    
    try:
        # Import the configuration
        from app.config import Settings
        
        # Create settings instance
        settings = Settings()
        
        print(f"✅ SUCCESS!")
        print(f"   CORS_ORIGINS: {settings.CORS_ORIGINS} (type: {type(settings.CORS_ORIGINS)})")
        print(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS} (type: {type(settings.ALLOWED_HOSTS)})")
        print(f"   API_KEY: {settings.API_KEY[:10]}... (length: {len(settings.API_KEY)})")
        
        # Test that CORS_ORIGINS is correctly parsed
        if settings.CORS_ORIGINS == ["*"]:
            print("✅ CORS_ORIGINS correctly parsed as ['*']")
        else:
            print(f"❌ CORS_ORIGINS incorrectly parsed as {settings.CORS_ORIGINS}")
            return False
            
        # Test that ALLOWED_HOSTS is correctly parsed
        if settings.ALLOWED_HOSTS == ["*"]:
            print("✅ ALLOWED_HOSTS correctly parsed as ['*']")
        else:
            print(f"❌ ALLOWED_HOSTS incorrectly parsed as {settings.ALLOWED_HOSTS}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Print more detailed error information
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        
        return False

def test_different_values():
    """Test different CORS_ORIGINS values"""
    
    print("\n" + "=" * 50)
    print("Testing different CORS_ORIGINS values...")
    
    test_cases = [
        ("*", "Single asterisk"),
        ("http://localhost", "Single URL"),
        ("http://localhost,https://localhost", "Comma-separated URLs"),
        ("", "Empty string"),
    ]
    
    for value, description in test_cases:
        print(f"\nTesting: {description} ('{value}')")
        
        # Set environment
        os.environ['CORS_ORIGINS'] = value
        
        try:
            # Clear module cache
            if 'app.config' in sys.modules:
                del sys.modules['app.config']
            
            from app.config import Settings
            settings = Settings()
            
            print(f"✅ SUCCESS: {settings.CORS_ORIGINS}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
        
        # Clean up
        if 'CORS_ORIGINS' in os.environ:
            del os.environ['CORS_ORIGINS']

if __name__ == "__main__":
    print("Pydantic Configuration Debug Test")
    print("=" * 50)
    
    # Test basic functionality
    basic_test_passed = test_pydantic_fix()
    
    if basic_test_passed:
        # Test different values
        test_different_values()
        print("\n🎉 All tests completed successfully!")
    else:
        print("\n💥 Basic test failed. Please check the configuration.")
        sys.exit(1)
