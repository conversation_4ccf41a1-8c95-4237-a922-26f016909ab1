#!/bin/bash

# HuggingFace Token Validation Script
# Validates HuggingFace token and MedGemma-4b-it model access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${PURPLE}[STEP $1]${NC} $2"
}

# Configuration
MODEL_NAME="google/medgemma-4b-it"
HF_API_BASE="https://huggingface.co/api"
TEST_TIMEOUT=30

# Function to display banner
show_banner() {
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              HuggingFace Token Validator                    ║"
    print_header "║                 MedGemma-4b-it Access                       ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to get token from various sources
get_huggingface_token() {
    local token=""
    
    # Check command line argument first
    if [[ $# -gt 0 && -n "$1" ]]; then
        token="$1"
        print_status "Using token from command line argument"
        return 0
    fi
    
    # Check environment variable
    if [[ -n "$HUGGINGFACE_TOKEN" ]]; then
        token="$HUGGINGFACE_TOKEN"
        print_status "Using token from HUGGINGFACE_TOKEN environment variable"
        echo "$token"
        return 0
    fi
    
    # Check .env.phase1 file
    if [[ -f ".env.phase1" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env.phase1 | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            print_status "Using token from .env.phase1 file"
            echo "$token"
            return 0
        fi
    fi
    
    # Check .env file
    if [[ -f ".env" ]]; then
        local env_token=$(grep "^HUGGINGFACE_TOKEN=" .env | cut -d'=' -f2- | tr -d '"' | tr -d "'")
        if [[ -n "$env_token" && "$env_token" != "hf_your_token_here_replace_with_actual_token" ]]; then
            token="$env_token"
            print_status "Using token from .env file"
            echo "$token"
            return 0
        fi
    fi
    
    # Check HF_TOKEN as fallback
    if [[ -n "$HF_TOKEN" ]]; then
        token="$HF_TOKEN"
        print_status "Using token from HF_TOKEN environment variable"
        echo "$token"
        return 0
    fi
    
    print_error "No HuggingFace token found!"
    print_status "Please provide a token using one of these methods:"
    print_status "1. Command line: $0 hf_your_token_here"
    print_status "2. Environment: export HUGGINGFACE_TOKEN=hf_your_token_here"
    print_status "3. .env.phase1 file: HUGGINGFACE_TOKEN=hf_your_token_here"
    return 1
}

# Function to validate token syntax
validate_token_syntax() {
    local token="$1"
    
    print_step "1" "Validating token syntax..."
    
    # Check if token is empty
    if [[ -z "$token" ]]; then
        print_error "Token is empty"
        return 1
    fi
    
    # Check token format (should start with hf_)
    if [[ ! "$token" =~ ^hf_[a-zA-Z0-9]{34}$ ]]; then
        print_error "Invalid token format"
        print_status "Expected format: hf_ followed by 34 alphanumeric characters"
        print_status "Your token: ${token:0:10}... (${#token} characters)"
        
        if [[ ! "$token" =~ ^hf_ ]]; then
            print_status "Token should start with 'hf_'"
        fi
        
        if [[ ${#token} -ne 37 ]]; then
            print_status "Token should be exactly 37 characters long (hf_ + 34 chars)"
        fi
        
        return 1
    fi
    
    print_success "Token syntax is valid"
    return 0
}

# Function to test basic HuggingFace API authentication
test_hf_authentication() {
    local token="$1"
    
    print_step "2" "Testing HuggingFace API authentication..."
    
    # Test whoami endpoint
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "$HF_API_BASE/whoami" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    case $http_code in
        200)
            print_success "Authentication successful"
            local username=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('name', 'Unknown'))" 2>/dev/null || echo "Unknown")
            print_status "Authenticated as: $username"
            return 0
            ;;
        401)
            print_error "Authentication failed - Invalid token"
            print_status "The token is not recognized by HuggingFace"
            return 1
            ;;
        403)
            print_error "Authentication failed - Token permissions insufficient"
            print_status "The token exists but lacks necessary permissions"
            return 1
            ;;
        *)
            print_error "Authentication test failed - HTTP $http_code"
            print_status "Response: $body"
            return 1
            ;;
    esac
}

# Function to test model access
test_model_access() {
    local token="$1"
    
    print_step "3" "Testing access to $MODEL_NAME..."
    
    # Test model info endpoint
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "$HF_API_BASE/models/$MODEL_NAME" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    case $http_code in
        200)
            print_success "Model access successful"
            
            # Extract model information
            if command -v python3 &> /dev/null; then
                local model_info=$(echo "$body" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print('Model ID: ' + str(data.get('id', 'Unknown')))
    print('Downloads: ' + str(data.get('downloads', 'Unknown')))
    print('Likes: ' + str(data.get('likes', 'Unknown')))
    print('Private: ' + str(data.get('private', False)))
    print('Gated: ' + str(data.get('gated', False)))
except:
    print('Could not parse model info')
" 2>/dev/null)
                print_status "Model information:"
                echo "$model_info" | while read line; do
                    print_status "  $line"
                done
            fi
            
            return 0
            ;;
        401)
            print_error "Model access failed - Authentication required"
            print_status "Your token is invalid or expired"
            return 1
            ;;
        403)
            print_error "Model access failed - Access denied"
            print_status "You don't have permission to access this model"
            print_status "This usually means:"
            print_status "  1. You haven't accepted the model's license agreement"
            print_status "  2. Your token doesn't have the required permissions"
            print_status "  3. The model requires special access approval"
            return 1
            ;;
        404)
            print_error "Model not found"
            print_status "The model $MODEL_NAME doesn't exist or has been moved"
            return 1
            ;;
        *)
            print_error "Model access test failed - HTTP $http_code"
            print_status "Response: $body"
            return 1
            ;;
    esac
}

# Function to test model file access
test_model_files() {
    local token="$1"
    
    print_step "4" "Testing model file access..."
    
    # Test model files endpoint
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "$HF_API_BASE/models/$MODEL_NAME/tree/main" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    case $http_code in
        200)
            print_success "Model files accessible"
            
            # Count files if possible
            if command -v python3 &> /dev/null; then
                local file_count=$(echo "$body" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    else:
        print('Unknown')
except:
    print('Unknown')
" 2>/dev/null)
                print_status "Model contains $file_count files"
            fi
            
            return 0
            ;;
        401|403)
            print_error "Model files access failed - Permission denied"
            print_status "You can see the model but cannot access its files"
            print_status "This indicates you haven't accepted the license agreement"
            return 1
            ;;
        404)
            print_error "Model files not found"
            return 1
            ;;
        *)
            print_error "Model files test failed - HTTP $http_code"
            print_status "Response: $body"
            return 1
            ;;
    esac
}

# Function to test actual model download capability
test_model_download() {
    local token="$1"
    
    print_step "5" "Testing model download capability..."
    
    # Test downloading a small file (config.json)
    local response
    local http_code
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --connect-timeout $TEST_TIMEOUT \
        -H "Authorization: Bearer $token" \
        "https://huggingface.co/$MODEL_NAME/resolve/main/config.json" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    case $http_code in
        200)
            print_success "Model download test successful"
            
            # Validate it's actually JSON
            if echo "$body" | python3 -c "import sys, json; json.load(sys.stdin)" 2>/dev/null; then
                print_status "Successfully downloaded and validated config.json"
            else
                print_warning "Downloaded file but content validation failed"
            fi
            
            return 0
            ;;
        401|403)
            print_error "Model download failed - Access denied"
            print_status "You cannot download model files"
            return 1
            ;;
        404)
            print_error "Model config file not found"
            return 1
            ;;
        *)
            print_error "Model download test failed - HTTP $http_code"
            return 1
            ;;
    esac
}

# Function to provide help and instructions
show_help_instructions() {
    echo ""
    print_header "📋 How to Fix HuggingFace Token Issues"
    echo ""
    
    print_status "1. Get a HuggingFace Token:"
    echo "   • Go to: https://huggingface.co/settings/tokens"
    echo "   • Click 'New token'"
    echo "   • Choose 'Read' permissions (minimum required)"
    echo "   • Copy the token (starts with hf_)"
    echo ""
    
    print_status "2. Accept MedGemma License:"
    echo "   • Go to: https://huggingface.co/$MODEL_NAME"
    echo "   • Read and accept the license agreement"
    echo "   • This is required for gated models like MedGemma"
    echo ""
    
    print_status "3. Configure Your Token:"
    echo "   • Method 1 - Environment file:"
    echo "     echo 'HUGGINGFACE_TOKEN=hf_your_token_here' >> .env.phase1"
    echo ""
    echo "   • Method 2 - Environment variable:"
    echo "     export HUGGINGFACE_TOKEN=hf_your_token_here"
    echo ""
    echo "   • Method 3 - Command line:"
    echo "     $0 hf_your_token_here"
    echo ""
    
    print_status "4. Verify Token:"
    echo "   • Run this script again: $0"
    echo "   • All tests should pass before deployment"
    echo ""
    
    print_status "5. Common Issues:"
    echo "   • Token format: Must start with 'hf_' and be 37 characters total"
    echo "   • License: Must accept MedGemma license on HuggingFace website"
    echo "   • Permissions: Token needs 'Read' access to repositories"
    echo "   • Expiration: Check if your token has expired"
    echo ""
}

# Function to run all validation tests
run_validation_tests() {
    local token="$1"
    local test_count=0
    local passed_count=0
    
    print_header "🧪 Running HuggingFace Token Validation Tests"
    echo ""
    
    # Test 1: Token syntax
    ((test_count++))
    if validate_token_syntax "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 2: Basic authentication
    ((test_count++))
    if test_hf_authentication "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 3: Model access
    ((test_count++))
    if test_model_access "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 4: Model files
    ((test_count++))
    if test_model_files "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Test 5: Model download
    ((test_count++))
    if test_model_download "$token"; then
        ((passed_count++))
    else
        return 1
    fi
    echo ""
    
    # Summary
    print_header "📊 Validation Summary"
    print_success "Passed: $passed_count/$test_count tests"
    
    if [[ $passed_count -eq $test_count ]]; then
        print_success "✅ All tests passed! Your token is ready for deployment."
        echo ""
        print_status "🚀 Next steps:"
        echo "  • Deploy Phase 1: docker-compose -f docker-compose.phase1.yml --env-file .env.phase1 up -d"
        echo "  • Monitor deployment: docker-compose -f docker-compose.phase1.yml logs -f"
        echo "  • Test deployment: ./scripts/test-phase1.sh"
        return 0
    else
        print_error "❌ Some tests failed. Please fix the issues before deployment."
        return 1
    fi
}

# Main function
main() {
    show_banner

    # Handle help flag
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [token]"
        echo ""
        echo "Validates HuggingFace token for MedGemma-4b-it model access"
        echo ""
        echo "This script automatically chooses the best validation method:"
        echo "  1. Official HuggingFace CLI (recommended)"
        echo "  2. Direct API calls (fallback)"
        echo ""
        echo "Arguments:"
        echo "  token    Optional HuggingFace token (if not provided, will look in env files)"
        echo ""
        echo "Examples:"
        echo "  $0                           # Auto-detect token from environment"
        echo "  $0 hf_your_token_here       # Test specific token"
        echo ""
        echo "Available validation scripts:"
        echo "  • ./scripts/validate-hf-token-cli.sh     # Official CLI version"
        echo "  • ./scripts/validate-hf-token-simple.sh  # Simple API version"
        echo ""
        show_help_instructions
        exit 0
    fi

    # Check if HuggingFace CLI is available and try CLI version first
    if command -v huggingface-cli &> /dev/null; then
        print_status "🚀 Using official HuggingFace CLI for validation..."
        if [[ -f "scripts/validate-hf-token-cli.sh" ]]; then
            exec ./scripts/validate-hf-token-cli.sh "$@"
        else
            print_warning "CLI validation script not found, falling back to API method"
        fi
    else
        print_warning "HuggingFace CLI not found, using API validation method"
        print_status "For better validation, install CLI: ./scripts/install-hf-cli.sh"
        echo ""
    fi
    
    # Check prerequisites
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_warning "python3 not found - some features will be limited"
    fi
    
    # Get token
    local token
    if ! token=$(get_huggingface_token "$@"); then
        echo ""
        show_help_instructions
        exit 1
    fi
    
    # Run validation tests
    if run_validation_tests "$token"; then
        exit 0
    else
        echo ""
        show_help_instructions
        exit 1
    fi
}

# Run main function
main "$@"
