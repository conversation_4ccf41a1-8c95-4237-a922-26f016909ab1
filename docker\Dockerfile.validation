# Docker-based HuggingFace Token Validation
# Uses the same environment as the MedGemma deployment for accurate validation

FROM python:3.11-slim as validation

# Set environment variables (same as MedGemma container)
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    HF_HOME=/app/model_cache \
    TRANSFORMERS_CACHE=/app/model_cache \
    HF_HUB_CACHE=/app/model_cache

# Install system dependencies (same as MedGemma)
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user (same as MedGemma)
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma

# Set working directory
WORKDIR /app

# Install Python dependencies for validation
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir \
    "huggingface-hub[cli]>=0.20.0" \
    "transformers>=4.50.0" \
    requests \
    python-dotenv

# Create necessary directories with proper permissions
RUN mkdir -p /app/model_cache /app/uploads /app/logs /app/validation && \
    chown -R medgemma:medgemma /app && \
    chmod -R 755 /app

# Copy validation scripts
COPY scripts/validate-hf-token-docker.py ./validation/
COPY scripts/docker-validation-startup.sh ./validation/

# Make scripts executable
RUN chmod +x ./validation/docker-validation-startup.sh && \
    chown -R medgemma:medgemma /app/validation

# Switch to non-root user
USER medgemma

# Set the validation script as entrypoint
ENTRYPOINT ["./validation/docker-validation-startup.sh"]
