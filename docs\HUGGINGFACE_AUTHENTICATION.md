# HuggingFace Authentication for MedGemma Model

This document explains how to handle HuggingFace authentication for downloading the MedGemma model during Docker builds and container runtime.

## 🔐 Overview

The MedGemma model (`google/medgemma-4b-it`) is a gated repository on HuggingFace that requires:
1. A valid HuggingFace account
2. Acceptance of the model's license agreement
3. A HuggingFace access token for authentication

## 📋 Prerequisites

### 1. Get HuggingFace Token
1. Visit [HuggingFace Settings](https://huggingface.co/settings/tokens)
2. Create a new token with "Read" permissions
3. Copy the token (starts with `hf_`)

### 2. Accept Model License
1. Visit [MedGemma Model Page](https://huggingface.co/google/medgemma-4b-it)
2. Read and accept the license agreement
3. Ensure your account has access to the model

## 🚀 Authentication Approaches

We provide three different approaches for handling HuggingFace authentication, each with different security and complexity trade-offs:

### Approach 1: BuildKit Secrets (RECOMMENDED) 🔒

**Security Level:** ⭐⭐⭐⭐⭐ (Highest)
**Complexity:** ⭐⭐⭐ (Medium)

This approach uses Docker BuildKit secrets to securely pass the token during build without exposing it in the final image or build history.

#### Setup:
```bash
# Create token file
echo "your_hf_token_here" > .hf_token
chmod 600 .hf_token

# Build using the provided script
./scripts/build-with-secrets.sh

# Or build manually
export DOCKER_BUILDKIT=1
docker buildx build \
  --secret id=huggingface_token,src=.hf_token \
  -f docker/Dockerfile.medgemma \
  -t medgemma-api .
```

#### Pros:
- ✅ Token never appears in image layers
- ✅ Token not visible in build history
- ✅ Most secure approach
- ✅ Recommended for production

#### Cons:
- ❌ Requires Docker BuildKit
- ❌ Slightly more complex setup

### Approach 2: Build Arguments 🔓

**Security Level:** ⭐⭐⭐ (Medium)
**Complexity:** ⭐⭐ (Low)

This approach uses Docker build arguments to pass the token during build.

#### Setup:
```bash
# Build using the provided script
./scripts/build-with-args.sh

# Or build manually
docker build \
  --build-arg HUGGINGFACE_TOKEN="your_token_here" \
  -f docker/Dockerfile.medgemma.buildarg \
  -t medgemma-api .

# Or with docker-compose (uncomment args section in docker-compose.yml)
export HUGGINGFACE_TOKEN="your_token_here"
docker-compose build
```

#### Pros:
- ✅ Simple to use
- ✅ Works with older Docker versions
- ✅ Model downloaded during build

#### Cons:
- ⚠️ Token visible in docker history
- ⚠️ Token may appear in build logs
- ⚠️ Less secure than secrets

### Approach 3: Runtime Download 🕐

**Security Level:** ⭐⭐⭐⭐ (High)
**Complexity:** ⭐ (Lowest)

This approach downloads the model when the container starts, not during build.

#### Setup:
```bash
# Build the image (no token needed)
docker build \
  -f docker/Dockerfile.medgemma.runtime \
  -t medgemma-api .

# Run with token as environment variable
docker run -e HUGGINGFACE_TOKEN="your_token_here" medgemma-api

# Or with docker-compose
export HUGGINGFACE_TOKEN="your_token_here"
docker-compose up
```

#### Pros:
- ✅ No token needed during build
- ✅ Token only in runtime environment
- ✅ Simplest build process
- ✅ Good for development

#### Cons:
- ⚠️ Longer container startup time
- ⚠️ Model downloaded every time (unless using volumes)
- ⚠️ Requires internet access at runtime

## 🛠 Configuration Files

### Docker Compose Configuration

The docker-compose files support all approaches:

```yaml
# docker-compose.dev.yml or docker-compose.yml
services:
  medgemma-api:
    build:
      context: .
      # Choose your approach:
      dockerfile: docker/Dockerfile.medgemma          # BuildKit secrets
      # dockerfile: docker/Dockerfile.medgemma.buildarg  # Build args
      # dockerfile: docker/Dockerfile.medgemma.runtime   # Runtime download
      
      # For build args approach, uncomment:
      # args:
      #   HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
    environment:
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}  # Always needed for runtime
```

### Environment Variables

Create a `.env` file with your token:

```bash
# .env
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here  # Alternative name
```

## 🔧 Build Scripts

We provide convenient build scripts for each approach:

### BuildKit Secrets (Recommended)
```bash
./scripts/build-with-secrets.sh [image_name] [dockerfile]
```

### Build Arguments
```bash
./scripts/build-with-args.sh [image_name] [dockerfile]
```

## 🐳 Docker Commands

### Using BuildKit Secrets
```bash
# Enable BuildKit
export DOCKER_BUILDKIT=1

# Build with secrets
docker buildx build \
  --secret id=huggingface_token,src=.hf_token \
  -t medgemma-api .

# Run container
docker run -p 8000:8000 \
  -e HUGGINGFACE_TOKEN="$(cat .hf_token)" \
  medgemma-api
```

### Using Build Arguments
```bash
# Build with args
docker build \
  --build-arg HUGGINGFACE_TOKEN="your_token" \
  -f docker/Dockerfile.medgemma.buildarg \
  -t medgemma-api .

# Run container
docker run -p 8000:8000 medgemma-api
```

### Using Runtime Download
```bash
# Build (no token needed)
docker build \
  -f docker/Dockerfile.medgemma.runtime \
  -t medgemma-api .

# Run with token
docker run -p 8000:8000 \
  -e HUGGINGFACE_TOKEN="your_token" \
  medgemma-api
```

## 🔍 Troubleshooting

### Common Issues

1. **"No HuggingFace token found"**
   - Ensure token is set in environment or .hf_token file
   - Check token format (should start with `hf_`)

2. **"You must have access to it and be authenticated"**
   - Accept the MedGemma license agreement
   - Verify token has correct permissions

3. **"401 Client Error"**
   - Token may be invalid or expired
   - Regenerate token on HuggingFace

4. **BuildKit not available**
   - Update Docker to latest version
   - Use build arguments approach instead

### Verification Commands

```bash
# Test token validity
python -c "
from huggingface_hub import login, HfApi
login(token='your_token_here')
api = HfApi()
print(api.model_info('google/medgemma-4b-it'))
"

# Check model cache
docker run --rm -v model_cache:/cache alpine ls -la /cache

# Test container startup
docker run --rm -p 8000:8000 \
  -e HUGGINGFACE_TOKEN="your_token" \
  medgemma-api
```

## 🏭 Production Recommendations

For production deployments:

1. **Use BuildKit Secrets** for maximum security
2. **Store tokens in secure secret management** (AWS Secrets Manager, Azure Key Vault, etc.)
3. **Use volume mounts** for model cache to avoid re-downloading
4. **Monitor token expiration** and rotate regularly
5. **Limit token permissions** to read-only

## ☁️ AWS EC2 Deployment

For AWS EC2 deployments, we provide specialized scripts and configurations using local environment files:

### EC2 Instance Requirements
- **Minimum**: t3.xlarge (4 vCPU, 16GB RAM)
- **Recommended**: t3.2xlarge (8 vCPU, 32GB RAM)
- **Storage**: 30GB+ EBS volume
- **OS**: Ubuntu 20.04+ or Amazon Linux 2

### Quick EC2 Setup
```bash
# 1. Setup EC2 instance
curl -fsSL https://raw.githubusercontent.com/your-repo/scripts/setup-ec2-instance.sh | bash

# 2. Configure environment
cp .env.example .env
nano .env  # Add your HuggingFace token

# 3. Deploy MedGemma
./scripts/deploy-ec2.sh
```

### Local Token Storage (Recommended)

#### Environment File (.env)
```bash
# Create .env file with secure permissions
cat > .env << EOF
HUGGINGFACE_TOKEN=hf_your_token_here
HF_TOKEN=hf_your_token_here
API_KEY=your-secure-api-key
REDIS_PASSWORD=your-redis-password
EOF

# Secure the file (owner read/write only)
chmod 600 .env

# Ensure .env is in .gitignore
echo ".env" >> .gitignore
```

#### Direct Environment Variables
```bash
# Export directly in shell
export HUGGINGFACE_TOKEN="hf_your_token_here"
export HF_TOKEN="hf_your_token_here"

# Or add to ~/.bashrc for persistence
echo 'export HUGGINGFACE_TOKEN="hf_your_token_here"' >> ~/.bashrc
```

### Security Best Practices

1. **File Permissions**: Always set `.env` file permissions to 600
   ```bash
   chmod 600 .env
   ```

2. **Version Control**: Never commit `.env` files
   ```bash
   echo ".env" >> .gitignore
   ```

3. **Token Validation**: Ensure tokens start with `hf_`
   ```bash
   # Valid token format
   HUGGINGFACE_TOKEN=hf_abcdef1234567890...
   ```

4. **Regular Rotation**: Rotate tokens periodically for security

### EC2 Security Group Configuration

Allow inbound traffic on these ports:
- **22** (SSH)
- **80** (HTTP)
- **443** (HTTPS)
- **8000** (FastAPI)
- **3000** (Grafana - optional)
- **9091** (Prometheus - optional)

## 📚 Additional Resources

- [HuggingFace Tokens Documentation](https://huggingface.co/docs/hub/security-tokens)
- [Docker BuildKit Secrets](https://docs.docker.com/develop/dev-best-practices/#use-multi-stage-builds)
- [MedGemma Model Page](https://huggingface.co/google/medgemma-4b-it)
- [AWS Systems Manager Parameter Store](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
- [AWS Secrets Manager](https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html)
