# Multi-stage build for MedGemma FastAPI service (Runtime Download Approach)
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r medgemma && useradd -r -g medgemma medgemma

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for CPU optimization
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu \
    accelerate \
    bitsandbytes \
    optimum

# Copy application code
COPY app/ ./app/
COPY scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/model_cache /app/uploads /app/logs && \
    chown -R medgemma:medgemma /app

# Create startup script that downloads model if needed
COPY scripts/startup.sh ./startup.sh
RUN chmod +x ./startup.sh && chown medgemma:medgemma ./startup.sh

# Switch to non-root user
USER medgemma

# Expose port
EXPOSE 8000

# Health check (with longer start period for model download)
HEALTHCHECK --interval=30s --timeout=10s --start-period=300s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run startup script that handles model download and app startup
CMD ["./startup.sh"]
