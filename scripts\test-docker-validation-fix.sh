#!/bin/bash

# Test script to verify the Docker validation fix
# This script tests the Docker validation without actually running the full validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running test: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "✅ $test_name: PASSED"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "❌ $test_name: FAILED"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to run a test with output
run_test_with_output() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running test: $test_name"
    
    if eval "$test_command"; then
        print_success "✅ $test_name: PASSED"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "❌ $test_name: FAILED"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Main test function
main() {
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║              Docker Validation Fix Test                     ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    # Test 1: Check if the problematic script exists
    run_test "Docker validation startup script exists" \
        "test -f scripts/docker-validation-startup.sh"
    
    # Test 2: Check bash syntax
    run_test "Docker validation startup script syntax" \
        "bash -n scripts/docker-validation-startup.sh"
    
    # Test 3: Check for the old Colors.NC pattern
    if grep -q "Colors\." scripts/docker-validation-startup.sh; then
        print_error "❌ Still found 'Colors.' pattern in the script"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    else
        print_success "✅ No 'Colors.' patterns found"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    fi
    
    # Test 4: Check if Dockerfile.validation exists
    run_test "Docker validation Dockerfile exists" \
        "test -f docker/Dockerfile.validation"
    
    # Test 5: Check if the main validation script exists
    run_test "Main Docker validation script exists" \
        "test -f scripts/validate-hf-token-docker.sh"
    
    # Test 6: Check if the Python validation script exists
    run_test "Python validation script exists" \
        "test -f scripts/validate-hf-token-docker.py"
    
    # Test 7: Check if Docker Compose validation file exists
    run_test "Docker Compose validation file exists" \
        "test -f docker-compose.validation.yml"
    
    # Test 8: Verify the fix by checking the specific line
    if grep -n "print_status()" scripts/docker-validation-startup.sh | grep -q "\${NC}"; then
        print_success "✅ Fixed line uses correct \${NC} variable"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_error "❌ Fixed line doesn't use correct \${NC} variable"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 9: Check if all color functions use correct variables
    local color_functions_ok=true
    for func in print_status print_success print_warning print_error print_header; do
        if grep -A 3 "^$func()" scripts/docker-validation-startup.sh | grep -q "Colors\."; then
            print_error "❌ Function $func still uses Colors. pattern"
            color_functions_ok=false
            TESTS_FAILED=$((TESTS_FAILED + 1))
            break
        fi
    done
    
    if $color_functions_ok; then
        print_success "✅ All color functions use correct variable names"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    fi
    
    # Test 10: Verify the script can be sourced without errors
    run_test "Script can be sourced without errors" \
        "bash -c 'source scripts/docker-validation-startup.sh && echo \"Sourced successfully\"' >/dev/null 2>&1"
    
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                        Test Summary                         ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    print_status "Total tests run: $total_tests"
    print_success "Tests passed: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        print_error "Tests failed: $TESTS_FAILED"
        echo ""
        print_error "💥 Some tests failed. Please review the issues above."
        return 1
    else
        print_success "Tests failed: $TESTS_FAILED"
        echo ""
        print_success "🎉 All tests passed! The Docker validation fix is working correctly."
        echo ""
        print_status "The bash syntax error has been successfully fixed:"
        print_status "• Changed \${BLUE}[INFO]\${Colors.NC} → \${BLUE}[INFO]\${NC}"
        print_status "• All color functions now use correct variable names"
        print_status "• Script syntax is valid and can be executed"
        print_status "• Docker validation should now work without bash substitution errors"
        return 0
    fi
}

# Run main function
main "$@"
