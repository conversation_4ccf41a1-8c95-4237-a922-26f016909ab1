# Phase 1: Standalone Core Model & API
# Fully functional AI chat API with direct access on port 8000
# Features: Complete MedGemma integration, chat, image analysis, conversation management

version: '3.8'

services:
  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
      args:
        HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
        HF_TOKEN: ${HUGGINGFACE_TOKEN}
    container_name: medgemma-api-phase1
    restart: unless-stopped
    environment:
      # Model Configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      - WORKERS=${WORKERS:-1}
      
      # API Configuration
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # Network Configuration
      - HOST=${HOST:-0.0.0.0}
      - PORT=${PORT:-8000}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-*}
      
      # File Upload Configuration
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      
      # Conversation Management
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - CONVERSATION_TTL=${CONVERSATION_TTL:-86400}
      
      # HuggingFace Authentication
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
    volumes:
      # Model cache for persistence across restarts
      - model_cache_phase1:/app/model_cache
      # Upload storage for images
      - upload_data_phase1:/app/uploads
      # Application logs
      - logs_phase1:/app/logs
      # Static frontend files
      - ./frontend:/app/frontend:ro
      
    ports:
      # Direct API access on port 8000
      - "8000:8000"
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # Allow time for model download
      
    depends_on:
      redis:
        condition: service_healthy
        
    deploy:
      resources:
        limits:
          memory: 14G  # Leave 2GB for system on t3.xlarge
        reservations:
          memory: 12G
          
    networks:
      - medgemma-network-phase1

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-phase1
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      # Persistent Redis data for conversation storage
      - redis_data_phase1:/data
      
    ports:
      # Expose Redis port for debugging (optional)
      - "6379:6379"
      
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
          
    networks:
      - medgemma-network-phase1

volumes:
  # Phase 1 specific volumes for data isolation
  model_cache_phase1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase1/model_cache
      
  upload_data_phase1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase1/uploads
      
  logs_phase1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase1/logs
      
  redis_data_phase1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase1/redis

networks:
  medgemma-network-phase1:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
