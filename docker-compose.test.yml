version: '3.8'

services:
  medgemma-api-test:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma.test
    container_name: medgemma-api-test
    environment:
      - MODEL_NAME=mock/test-model
      - API_KEY=dev-api-key-for-testing-only
      - DEBUG=true
      - ENVIRONMENT=test
      - USE_MOCK_MODEL=true
      - REDIS_URL=redis://redis-test:6379
      - REDIS_PASSWORD=test-password
    ports:
      - "8001:8000"
    depends_on:
      redis-test:
        condition: service_started
    networks:
      - test-network

  nginx-test:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx.dev
    container_name: nginx-test
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.test.conf:/etc/nginx/conf.d/default.conf:ro
      - ./frontend:/usr/share/nginx/html:ro
    depends_on:
      medgemma-api-test:
        condition: service_started
    networks:
      - test-network

  redis-test:
    image: redis:7-alpine
    container_name: redis-test
    command: redis-server --appendonly yes --requirepass test-password
    volumes:
      - test_redis_data:/data
    networks:
      - test-network

volumes:
  test_redis_data:

networks:
  test-network:
    driver: bridge
