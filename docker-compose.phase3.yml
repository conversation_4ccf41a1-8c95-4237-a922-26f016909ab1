# Phase 3: SSL/TLS Security (HTTPS)
# Extends Phase 2 with SSL/TLS encryption and security headers
# Features: All Phase 1 & 2 + SSL certificates, HTTPS redirect, security headers

version: '3.8'

services:
  nginx:
    build:
      context: .
      dockerfile: docker/Dockerfile.nginx
    container_name: medgemma-nginx-phase3
    restart: unless-stopped
    
    ports:
      # HTTP (for redirect to HTTPS)
      - "80:80"
      # HTTPS
      - "443:443"
      
    volumes:
      # Nginx configuration for SSL
      - ./nginx/nginx.phase3.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/medgemma.phase3.conf:/etc/nginx/conf.d/default.conf:ro
      # Static frontend files
      - ./frontend:/usr/share/nginx/html:ro
      # SSL certificates
      - ssl_certs_phase3:/etc/nginx/ssl:ro
      - certbot_webroot_phase3:/var/www/certbot
      # Nginx logs
      - nginx_logs_phase3:/var/log/nginx
      
    environment:
      - API_UPSTREAM=medgemma-api:8000
      - DOMAIN_NAME=${DOMAIN_NAME}
      - SSL_EMAIL=${SSL_EMAIL}
      - NGINX_CLIENT_MAX_BODY_SIZE=${NGINX_CLIENT_MAX_BODY_SIZE:-10m}
      - NGINX_WORKER_PROCESSES=${NGINX_WORKER_PROCESSES:-auto}
      
    depends_on:
      medgemma-api:
        condition: service_healthy
        
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    networks:
      - medgemma-network-phase3

  certbot:
    image: certbot/certbot:latest
    container_name: medgemma-certbot-phase3
    restart: "no"
    
    volumes:
      # SSL certificates storage
      - ssl_certs_phase3:/etc/letsencrypt
      - certbot_webroot_phase3:/var/www/certbot
      # Certbot logs
      - certbot_logs_phase3:/var/log/letsencrypt
      
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
      - SSL_EMAIL=${SSL_EMAIL}
      - CERTBOT_STAGING=${CERTBOT_STAGING:-false}
      
    # Certbot runs on-demand, not as a service
    profiles:
      - certbot
      
    networks:
      - medgemma-network-phase3

  medgemma-api:
    build:
      context: .
      dockerfile: docker/Dockerfile.medgemma
      args:
        HUGGINGFACE_TOKEN: ${HUGGINGFACE_TOKEN}
        HF_TOKEN: ${HUGGINGFACE_TOKEN}
    container_name: medgemma-api-phase3
    restart: unless-stopped
    
    environment:
      # Model Configuration
      - MODEL_NAME=${MODEL_NAME:-google/medgemma-4b-it}
      - MAX_LENGTH=${MAX_LENGTH:-2048}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      - TOP_P=${TOP_P:-0.9}
      - WORKERS=${WORKERS:-1}
      
      # API Configuration
      - API_KEY=${API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # Network Configuration (internal only)
      - HOST=0.0.0.0
      - PORT=8000
      - CORS_ORIGINS=${CORS_ORIGINS:-https://${DOMAIN_NAME},https://www.${DOMAIN_NAME}}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-${DOMAIN_NAME},www.${DOMAIN_NAME}}
      
      # File Upload Configuration
      - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}
      
      # Conversation Management
      - CONVERSATION_HISTORY_LIMIT=${CONVERSATION_HISTORY_LIMIT:-50}
      - CONVERSATION_TTL=${CONVERSATION_TTL:-86400}
      
      # HuggingFace Authentication
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - HF_TOKEN=${HUGGINGFACE_TOKEN}
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
    volumes:
      # Reuse Phase 2 data or create new Phase 3 data
      - model_cache_phase3:/app/model_cache
      - upload_data_phase3:/app/uploads
      - logs_phase3:/app/logs
      
    # No external port exposure - access through Nginx only
    expose:
      - "8000"
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
      
    depends_on:
      redis:
        condition: service_healthy
        
    deploy:
      resources:
        limits:
          memory: 14G
        reservations:
          memory: 12G
          
    networks:
      - medgemma-network-phase3

  redis:
    image: redis:7-alpine
    container_name: medgemma-redis-phase3
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      - redis_data_phase3:/data
      
    expose:
      - "6379"
      
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
          
    networks:
      - medgemma-network-phase3

volumes:
  # Phase 3 specific volumes
  model_cache_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/model_cache
      
  upload_data_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/uploads
      
  logs_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/logs
      
  nginx_logs_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/nginx_logs
      
  ssl_certs_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/ssl_certs
      
  certbot_webroot_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/certbot_webroot
      
  certbot_logs_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/certbot_logs
      
  redis_data_phase3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/phase3/redis

networks:
  medgemma-network-phase3:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
